import React, { useState, useEffect } from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../context/LanguageContext';
import EnhancedScholarshipCard from './EnhancedScholarshipCard';
import UnifiedSidebar from './UnifiedSidebar';
import CommentsSection from './CommentsSection';
import AdPlacement from './AdPlacement';
import { Pagination, Spin, Alert } from 'antd';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
  fundingSource?: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

interface HeroArticle {
  category: string;
  content: string[];
}

interface LevelPageConfig {
  level: string;
  title: string;
  description: string;
  keywords: string;
  heroTitle: string;
  heroSubtitle: string;
  heroArticle?: HeroArticle;
  infoTitle: string;
  infoContent: string;
  benefits: string[];
  apiEndpoint: string;
}

interface StandardLevelPageProps {
  config: LevelPageConfig;
}

const StandardLevelPage: React.FC<StandardLevelPageProps> = ({ config }) => {
  const { translations } = useLanguage();
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 6,
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Handle scholarship card click
  const handleScholarshipClick = (id: number, slug?: string) => {
    if (slug) {
      window.location.href = `/bourse/${slug}`;
    } else {
      window.location.href = `/scholarships/${id}`;
    }
  };

  const fetchScholarships = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const params = new URLSearchParams({
        level: config.level,
        page: pagination.page.toString(),
        limit: pagination.limit.toString()
      });

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      console.log('API Response:', data); // Debug log

      if (data.success) {
        const scholarshipsData = data.data || [];
        setScholarships(scholarshipsData);

        // Set pagination data with fallback values
        const paginationData = {
          total: data.pagination?.total || scholarshipsData.length || 0,
          totalPages: data.pagination?.totalPages || Math.ceil((scholarshipsData.length || 0) / pagination.limit),
          hasNextPage: data.pagination?.hasNextPage || false,
          hasPreviousPage: data.pagination?.hasPreviousPage || false
        };

        console.log('Pagination Data:', paginationData); // Debug log

        setPagination(prev => ({
          ...prev,
          ...paginationData
        }));
      } else {
        throw new Error(data.message || 'Failed to load scholarships');
      }
    } catch (error) {
      console.error('Error fetching scholarships:', error);
      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, config.level]);

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, page }));
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const sidebarConfig = {
    type: 'levels' as const,
    currentItem: config.level,
    limit: 10
  };

  return (
    <>
      <Helmet>
        <title>{config.title}</title>
        <meta name="description" content={config.description} />
        <meta name="keywords" content={config.keywords} />
      </Helmet>

      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-16 sm:pt-18 md:pt-20 lg:pt-22">


        {/* Hero Article with Integrated Title - Clean Professional */}
        {config.heroArticle && (
          <div className="bg-gray-100 py-6 md:py-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <article>
                {/* Main Title - Integrated */}
                <div className="mb-6">
                  <h1 className="text-2xl md:text-3xl font-bold text-gray-900 mb-2">
                    Bourses de {config.level} Disponibles
                  </h1>
                  <p className="text-gray-600 text-base">
                    Explorez nos opportunités de financement pour vos études de {config.level.toLowerCase()}
                  </p>
                </div>

                {/* Article Content - Full Width */}
                <div className="prose max-w-none">
                  {config.heroArticle.content.map((paragraph, index) => (
                    <p key={index} className="text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify">
                      {paragraph.split('\n').map((line, lineIndex) => (
                        <span key={lineIndex}>
                          {line}
                          {lineIndex < paragraph.split('\n').length - 1 && <br />}
                        </span>
                      ))}
                    </p>
                  ))}
                </div>
              </article>
            </div>
          </div>
        )}

        {/* Desktop Ad - Only visible on large screens */}
        <div className="hidden lg:block py-8 bg-white">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <AdPlacement
              adSlot="1234567890"
              adSize="leaderboard"
              responsive={true}
            />
          </div>
        </div>

        {/* Content Section */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 lg:py-16">
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Main Content */}
            <div className="lg:w-2/3">


              {/* Scholarships Grid - Clean & Focused */}
              <div id="scholarships-section" className="mb-8">


                {loading ? (
                  <div className="flex justify-center items-center py-16">
                    <Spin size="large" tip="Chargement des bourses..." />
                  </div>
                ) : error ? (
                  <Alert
                    message="Erreur"
                    description={error}
                    type="error"
                    showIcon
                    className="mb-6 rounded-xl shadow-md"
                  />
                ) : (
                  <>
                    {/* Mobile Ad - Only visible on small screens */}
                    <div className="mb-8 md:hidden">
                      <AdPlacement
                        adSlot="4567890123"
                        adSize="rectangle"
                        responsive={true}
                        fullWidth={true}
                      />
                    </div>

                    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2">
                      {scholarships.map((scholarship, index) => (
                        <div key={scholarship.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                          <EnhancedScholarshipCard
                            id={scholarship.id}
                            title={scholarship.title}
                            thumbnail={scholarship.thumbnail}
                            deadline={scholarship.deadline}
                            isOpen={scholarship.isOpen}
                            level={scholarship.level}
                            country={scholarship.country}
                            fundingSource={scholarship.fundingSource}
                            onClick={handleScholarshipClick}
                            index={index}
                          />
                        </div>
                      ))}
                    </div>

                    {/* Professional Pagination */}
                    {(pagination.total > pagination.limit || scholarships.length > 0) && (
                      <div className="flex justify-center mt-12">
                        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                          <Pagination
                            current={pagination.page}
                            total={Math.max(pagination.total, scholarships.length)}
                            pageSize={pagination.limit}
                            onChange={handlePageChange}
                            showSizeChanger={false}
                            showQuickJumper={false}
                            size="default"
                            className="professional-pagination"
                          />
                          <div className="text-center mt-2 text-sm text-gray-500">
                            {scholarships.length > 0 && `${scholarships.length} bourses affichées`}
                          </div>
                        </div>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>

            {/* Sidebar */}
            <UnifiedSidebar
              config={sidebarConfig}
              className="lg:w-1/3"
            />
          </div>
        </div>

        {/* Info Section - Fully Responsive */}
        <div className="bg-white py-4 md:py-6 lg:py-8 border-t border-gray-100">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <div id="info-section">
                <h2 className="text-base md:text-lg lg:text-xl font-semibold text-gray-900 mb-3 md:mb-4">
                  {config.infoTitle}
                </h2>

                <p className="text-gray-700 leading-relaxed mb-4 md:mb-6 text-sm md:text-base text-justify">
                  {config.infoContent}
                </p>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3">
                  {config.benefits.map((benefit, index) => (
                    <div key={index} className="flex items-start space-x-2 md:space-x-3">
                      <svg className="w-3 h-3 md:w-4 md:h-4 text-green-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span className="text-gray-700 text-xs md:text-sm leading-relaxed">
                        {benefit}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Comments Section */}
        <div className="bg-gray-50 py-8">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <CommentsSection
              pageType="level"
              pageId={config.level}
            />
          </div>
        </div>


      </div>
    </>
  );
};

export default StandardLevelPage;
