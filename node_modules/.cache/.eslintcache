[{"/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx": "1", "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx": "2", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx": "3", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx": "4", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx": "5", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx": "6", "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx": "7", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx": "8", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx": "9", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx": "10", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx": "11", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx": "12", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx": "13", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx": "14", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx": "15", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts": "16", "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts": "17", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx": "18", "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx": "19", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts": "20", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts": "21", "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts": "22", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx": "23", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx": "24", "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx": "25", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx": "26", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx": "27", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx": "28", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx": "29", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx": "30", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx": "31", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx": "32", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx": "33", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx": "34", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx": "35", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx": "36", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx": "37", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx": "38", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx": "39", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx": "40", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx": "41", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx": "42", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx": "43", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx": "44", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts": "45", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts": "46", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts": "47", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx": "48", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx": "49", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx": "50", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx": "51", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx": "52", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx": "53", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts": "54", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx": "55", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx": "56", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx": "57", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx": "58", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx": "59", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx": "60", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx": "61", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx": "62", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx": "63", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx": "64", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx": "65", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx": "66", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx": "67", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx": "68", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx": "69", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts": "70", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx": "71", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx": "72", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx": "73", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx": "74", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx": "75", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts": "76", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts": "77", "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx": "78", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx": "79", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx": "80", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx": "81", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx": "82", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx": "83", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx": "84", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx": "85", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx": "86", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx": "87", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx": "88", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx": "89", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx": "90", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx": "91", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx": "92", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx": "93", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx": "94", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx": "95", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx": "96", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx": "97", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx": "98", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx": "99", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx": "100", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx": "101", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts": "102", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx": "103", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts": "104", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts": "105", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx": "106", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx": "107", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx": "108", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx": "109", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx": "110", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx": "111", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx": "112", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx": "113", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx": "114", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx": "115", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx": "116", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts": "117", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx": "118", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts": "119", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx": "120", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx": "121", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx": "122", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx": "123", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx": "124", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx": "125", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx": "126", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx": "127", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx": "128", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx": "129", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx": "130", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx": "131", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx": "132", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx": "133", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx": "134", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx": "135", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx": "136", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx": "137", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts": "138", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts": "139", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts": "140", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts": "141", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx": "142", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx": "143", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx": "144", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts": "145", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts": "146", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx": "147", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts": "148", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx": "149", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx": "150", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx": "151", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx": "152", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx": "153", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx": "154", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx": "155", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx": "156", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx": "157", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx": "158", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx": "159", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts": "160", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx": "161", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx": "162", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx": "163", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx": "164", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx": "165", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx": "166", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts": "167", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx": "168", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx": "169", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts": "170", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx": "171", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx": "172", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx": "173", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx": "174", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx": "175", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx": "176", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardCountryPage.tsx": "177", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/filters/StandardizedFilters.tsx": "178", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/DoctoratePage.tsx": "179", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UnifiedSidebar.tsx": "180", "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/CommentsSection.tsx": "181"}, {"size": 274, "mtime": 1745947759865, "results": "182", "hashOfConfig": "183"}, {"size": 2118, "mtime": 1745983514069, "results": "184", "hashOfConfig": "183"}, {"size": 5438, "mtime": 1745977740794, "results": "185", "hashOfConfig": "183"}, {"size": 10242, "mtime": 1745981563339, "results": "186", "hashOfConfig": "183"}, {"size": 1742, "mtime": 1745977713025, "results": "187", "hashOfConfig": "183"}, {"size": 7131, "mtime": 1745981562258, "results": "188", "hashOfConfig": "183"}, {"size": 10498, "mtime": 1745982258490, "results": "189", "hashOfConfig": "183"}, {"size": 3076, "mtime": 1745945973317, "results": "190", "hashOfConfig": "183"}, {"size": 553, "mtime": 1745978322072, "results": "191", "hashOfConfig": "183"}, {"size": 6250, "mtime": 1745977773426, "results": "192", "hashOfConfig": "183"}, {"size": 2446, "mtime": 1745945132621, "results": "193", "hashOfConfig": "183"}, {"size": 2645, "mtime": 1745977581979, "results": "194", "hashOfConfig": "183"}, {"size": 4167, "mtime": 1745981843303, "results": "195", "hashOfConfig": "183"}, {"size": 5713, "mtime": 1745981758114, "results": "196", "hashOfConfig": "183"}, {"size": 675, "mtime": 1745976791748, "results": "197", "hashOfConfig": "183"}, {"size": 1059, "mtime": 1745976720607, "results": "198", "hashOfConfig": "183"}, {"size": 3452, "mtime": 1745946003719, "results": "199", "hashOfConfig": "183"}, {"size": 2518, "mtime": 1745983866923, "results": "200", "hashOfConfig": "183"}, {"size": 1737, "mtime": 1745978376608, "results": "201", "hashOfConfig": "183"}, {"size": 4075, "mtime": 1745982269507, "results": "202", "hashOfConfig": "183"}, {"size": 4531, "mtime": 1745982263875, "results": "203", "hashOfConfig": "183"}, {"size": 5406, "mtime": 1745982274929, "results": "204", "hashOfConfig": "183"}, {"size": 2535, "mtime": 1745978386143, "results": "205", "hashOfConfig": "183"}, {"size": 737, "mtime": 1745944438688, "results": "206", "hashOfConfig": "183"}, {"size": 2323, "mtime": 1745982233889, "results": "207", "hashOfConfig": "183"}, {"size": 274, "mtime": 1745947759865, "results": "208", "hashOfConfig": "209"}, {"size": 4324, "mtime": 1746276088446, "results": "210", "hashOfConfig": "209"}, {"size": 7131, "mtime": 1745981562258, "results": "211", "hashOfConfig": "209"}, {"size": 10498, "mtime": 1745982258490, "results": "212", "hashOfConfig": "209"}, {"size": 10242, "mtime": 1745981563339, "results": "213", "hashOfConfig": "209"}, {"size": 1742, "mtime": 1745977713025, "results": "214", "hashOfConfig": "209"}, {"size": 8315, "mtime": 1746204095547, "results": "215", "hashOfConfig": "209"}, {"size": 3076, "mtime": 1745945973317, "results": "216", "hashOfConfig": "209"}, {"size": 2535, "mtime": 1746033992389, "results": "217", "hashOfConfig": "209"}, {"size": 1737, "mtime": 1745978376608, "results": "218", "hashOfConfig": "209"}, {"size": 6250, "mtime": 1745977773426, "results": "219", "hashOfConfig": "209"}, {"size": 553, "mtime": 1745978322072, "results": "220", "hashOfConfig": "209"}, {"size": 2446, "mtime": 1745945132621, "results": "221", "hashOfConfig": "209"}, {"size": 5154, "mtime": 1746276100698, "results": "222", "hashOfConfig": "209"}, {"size": 8097, "mtime": 1745983506688, "results": "223", "hashOfConfig": "209"}, {"size": 6024, "mtime": 1746026061709, "results": "224", "hashOfConfig": "209"}, {"size": 9559, "mtime": 1746270350218, "results": "225", "hashOfConfig": "209"}, {"size": 737, "mtime": 1745944438688, "results": "226", "hashOfConfig": "209"}, {"size": 2645, "mtime": 1745977581979, "results": "227", "hashOfConfig": "209"}, {"size": 4531, "mtime": 1745982263875, "results": "228", "hashOfConfig": "209"}, {"size": 4075, "mtime": 1745982269507, "results": "229", "hashOfConfig": "209"}, {"size": 5406, "mtime": 1745982274929, "results": "230", "hashOfConfig": "209"}, {"size": 5713, "mtime": 1745981758114, "results": "231", "hashOfConfig": "209"}, {"size": 675, "mtime": 1745976791748, "results": "232", "hashOfConfig": "209"}, {"size": 4167, "mtime": 1745981843303, "results": "233", "hashOfConfig": "209"}, {"size": 2115, "mtime": 1746029576846, "results": "234", "hashOfConfig": "209"}, {"size": 1689, "mtime": 1745982730905, "results": "235", "hashOfConfig": "209"}, {"size": 19288, "mtime": 1745983400384, "results": "236", "hashOfConfig": "209"}, {"size": 1059, "mtime": 1745976720607, "results": "237", "hashOfConfig": "209"}, {"size": 2535, "mtime": 1745978386143, "results": "238", "hashOfConfig": "209"}, {"size": 14052, "mtime": 1746274142489, "results": "239", "hashOfConfig": "209"}, {"size": 3939, "mtime": 1746017528736, "results": "240", "hashOfConfig": "209"}, {"size": 13857, "mtime": 1746282401482, "results": "241", "hashOfConfig": "209"}, {"size": 11940, "mtime": 1746252382064, "results": "242", "hashOfConfig": "209"}, {"size": 38877, "mtime": 1746252407519, "results": "243", "hashOfConfig": "209"}, {"size": 8393, "mtime": 1746249939564, "results": "244", "hashOfConfig": "209"}, {"size": 11544, "mtime": 1746272209267, "results": "245", "hashOfConfig": "209"}, {"size": 1343, "mtime": 1746033436995, "results": "246", "hashOfConfig": "209"}, {"size": 1752, "mtime": 1746274101102, "results": "247", "hashOfConfig": "209"}, {"size": 3341, "mtime": 1746199132190, "results": "248", "hashOfConfig": "209"}, {"size": 7045, "mtime": 1746199160974, "results": "249", "hashOfConfig": "209"}, {"size": 4103, "mtime": 1746200520123, "results": "250", "hashOfConfig": "209"}, {"size": 6276, "mtime": 1746249196201, "results": "251", "hashOfConfig": "209"}, {"size": 7222, "mtime": 1746249216418, "results": "252", "hashOfConfig": "209"}, {"size": 1105, "mtime": 1746201832350, "results": "253", "hashOfConfig": "209"}, {"size": 921, "mtime": 1746202207790, "results": "254", "hashOfConfig": "209"}, {"size": 11379, "mtime": 1746276323665, "results": "255", "hashOfConfig": "209"}, {"size": 1059, "mtime": 1746226321253, "results": "256", "hashOfConfig": "209"}, {"size": 7914, "mtime": 1746251582912, "results": "257", "hashOfConfig": "209"}, {"size": 4784, "mtime": 1746252717773, "results": "258", "hashOfConfig": "209"}, {"size": 1777, "mtime": 1746254015165, "results": "259", "hashOfConfig": "209"}, {"size": 9435, "mtime": 1746252638103, "results": "260", "hashOfConfig": "209"}, {"size": 5504, "mtime": 1746275135511, "results": "261", "hashOfConfig": "209"}, {"size": 1053, "mtime": 1752353080389, "results": "262", "hashOfConfig": "263"}, {"size": 6124, "mtime": 1752567826307, "results": "264", "hashOfConfig": "263"}, {"size": 13818, "mtime": 1752668855725, "results": "265", "hashOfConfig": "263"}, {"size": 19195, "mtime": 1747280789154, "results": "266", "hashOfConfig": "263"}, {"size": 3587, "mtime": 1747235972243, "results": "267", "hashOfConfig": "263"}, {"size": 10653, "mtime": 1747235897205, "results": "268", "hashOfConfig": "263"}, {"size": 3076, "mtime": 1745945973317, "results": "269", "hashOfConfig": "263"}, {"size": 1974, "mtime": 1747278879917, "results": "270", "hashOfConfig": "263"}, {"size": 577, "mtime": 1752352136699, "results": "271", "hashOfConfig": "263"}, {"size": 18635, "mtime": 1747235596822, "results": "272", "hashOfConfig": "273"}, {"size": 2446, "mtime": 1745945132621, "results": "274", "hashOfConfig": "263"}, {"size": 19874, "mtime": 1752251800113, "results": "275", "hashOfConfig": "263"}, {"size": 5624, "mtime": 1752347411216, "results": "276", "hashOfConfig": "263"}, {"size": 11326, "mtime": 1752283692783, "results": "277", "hashOfConfig": "263"}, {"size": 14078, "mtime": 1752283590200, "results": "278", "hashOfConfig": "263"}, {"size": 12834, "mtime": 1752553805022, "results": "279", "hashOfConfig": "263"}, {"size": 7045, "mtime": 1746199160974, "results": "280", "hashOfConfig": "263"}, {"size": 3341, "mtime": 1746199132190, "results": "281", "hashOfConfig": "263"}, {"size": 1059, "mtime": 1746226321253, "results": "282", "hashOfConfig": "263"}, {"size": 6276, "mtime": 1746249196201, "results": "283", "hashOfConfig": "263"}, {"size": 921, "mtime": 1746202207790, "results": "284", "hashOfConfig": "263"}, {"size": 1695, "mtime": 1747186871230, "results": "285", "hashOfConfig": "273"}, {"size": 3161, "mtime": 1747232764014, "results": "286", "hashOfConfig": "273"}, {"size": 7071, "mtime": 1752353425661, "results": "287", "hashOfConfig": "263"}, {"size": 12973, "mtime": 1747224498475, "results": "288", "hashOfConfig": "263"}, {"size": 6490, "mtime": 1752353387940, "results": "289", "hashOfConfig": "263"}, {"size": 8394, "mtime": 1752353443428, "results": "290", "hashOfConfig": "263"}, {"size": 8023, "mtime": 1752346082272, "results": "291", "hashOfConfig": "263"}, {"size": 675, "mtime": 1745976791748, "results": "292", "hashOfConfig": "273"}, {"size": 7860, "mtime": 1752251788545, "results": "293", "hashOfConfig": "263"}, {"size": 7222, "mtime": 1746249216418, "results": "294", "hashOfConfig": "263"}, {"size": 9917, "mtime": 1752283723085, "results": "295", "hashOfConfig": "263"}, {"size": 5948, "mtime": 1752352806967, "results": "296", "hashOfConfig": "263"}, {"size": 2535, "mtime": 1745978386143, "results": "297", "hashOfConfig": "263"}, {"size": 3877, "mtime": 1747235933700, "results": "298", "hashOfConfig": "263"}, {"size": 1689, "mtime": 1745982730905, "results": "299", "hashOfConfig": "263"}, {"size": 3697, "mtime": 1747184461868, "results": "300", "hashOfConfig": "273"}, {"size": 959, "mtime": 1747186815101, "results": "301", "hashOfConfig": "273"}, {"size": 5906, "mtime": 1752553992443, "results": "302", "hashOfConfig": "263"}, {"size": 2847, "mtime": 1747187027857, "results": "303", "hashOfConfig": "263"}, {"size": 2604, "mtime": 1747279467729, "results": "304", "hashOfConfig": "263"}, {"size": 8119, "mtime": 1747220020952, "results": "305", "hashOfConfig": "273"}, {"size": 8243, "mtime": 1747220059414, "results": "306", "hashOfConfig": "273"}, {"size": 3071, "mtime": 1747221577347, "results": "307", "hashOfConfig": "273"}, {"size": 6125, "mtime": 1747221750779, "results": "308", "hashOfConfig": "273"}, {"size": 6017, "mtime": 1747221715802, "results": "309", "hashOfConfig": "273"}, {"size": 3890, "mtime": 1747221780672, "results": "310", "hashOfConfig": "273"}, {"size": 3377, "mtime": 1747221613654, "results": "311", "hashOfConfig": "273"}, {"size": 3156, "mtime": 1747221640258, "results": "312", "hashOfConfig": "273"}, {"size": 7752, "mtime": 1747237735157, "results": "313", "hashOfConfig": "263"}, {"size": 9067, "mtime": 1752639657517, "results": "314", "hashOfConfig": "263"}, {"size": 9820, "mtime": 1747279005156, "results": "315", "hashOfConfig": "263"}, {"size": 9944, "mtime": 1752639636981, "results": "316", "hashOfConfig": "263"}, {"size": 6847, "mtime": 1752639686951, "results": "317", "hashOfConfig": "263"}, {"size": 9620, "mtime": 1747242054549, "results": "318", "hashOfConfig": "263"}, {"size": 9567, "mtime": 1747242099457, "results": "319", "hashOfConfig": "263"}, {"size": 4029, "mtime": 1752669213446, "results": "320", "hashOfConfig": "263"}, {"size": 7620, "mtime": 1752639746220, "results": "321", "hashOfConfig": "263"}, {"size": 28904, "mtime": 1752248907873, "results": "322", "hashOfConfig": "263"}, {"size": 1536, "mtime": 1747237627552, "results": "323", "hashOfConfig": "263"}, {"size": 2783, "mtime": 1747275467037, "results": "324", "hashOfConfig": "263"}, {"size": 4730, "mtime": 1747275582856, "results": "325", "hashOfConfig": "263"}, {"size": 5097, "mtime": 1747302359006, "results": "326", "hashOfConfig": "273"}, {"size": 1008, "mtime": 1752283918060, "results": "327", "hashOfConfig": "263"}, {"size": 9653, "mtime": 1752283637791, "results": "328", "hashOfConfig": "263"}, {"size": 358, "mtime": 1752254238558, "results": "329", "hashOfConfig": "263"}, {"size": 3337, "mtime": 1752554200802, "results": "330", "hashOfConfig": "263"}, {"size": 1433, "mtime": 1752283753270, "results": "331", "hashOfConfig": "263"}, {"size": 3435, "mtime": 1752283114606, "results": "332", "hashOfConfig": "263"}, {"size": 4212, "mtime": 1752553908165, "results": "333", "hashOfConfig": "263"}, {"size": 1083, "mtime": 1752283244823, "results": "334", "hashOfConfig": "263"}, {"size": 5685, "mtime": 1752287194809, "results": "335", "hashOfConfig": "263"}, {"size": 16723, "mtime": 1752348343896, "results": "336", "hashOfConfig": "263"}, {"size": 14378, "mtime": 1752304560424, "results": "337", "hashOfConfig": "263"}, {"size": 16323, "mtime": 1747156326467, "results": "338", "hashOfConfig": "263"}, {"size": 10759, "mtime": 1752646977608, "results": "339", "hashOfConfig": "263"}, {"size": 11085, "mtime": 1752647133600, "results": "340", "hashOfConfig": "263"}, {"size": 15674, "mtime": 1752642648936, "results": "341", "hashOfConfig": "263"}, {"size": 2511, "mtime": 1752574238553, "results": "342", "hashOfConfig": "263"}, {"size": 10639, "mtime": 1752347902146, "results": "343", "hashOfConfig": "263"}, {"size": 14557, "mtime": 1752347930663, "results": "344", "hashOfConfig": "263"}, {"size": 12835, "mtime": 1752351140445, "results": "345", "hashOfConfig": "263"}, {"size": 10574, "mtime": 1752639498698, "results": "346", "hashOfConfig": "263"}, {"size": 6836, "mtime": 1752590105883, "results": "347", "hashOfConfig": "263"}, {"size": 7078, "mtime": 1752638859528, "results": "348", "hashOfConfig": "263"}, {"size": 7609, "mtime": 1752571252778, "results": "349", "hashOfConfig": "263"}, {"size": 4433, "mtime": 1752389001452, "results": "350", "hashOfConfig": "263"}, {"size": 18226, "mtime": 1752381116505, "results": "351", "hashOfConfig": "263"}, {"size": 11905, "mtime": 1752554305331, "results": "352", "hashOfConfig": "263"}, {"size": 12528, "mtime": 1752388836464, "results": "353", "hashOfConfig": "263"}, {"size": 17214, "mtime": 1752668931331, "results": "354", "hashOfConfig": "263"}, {"size": 6616, "mtime": 1752388753535, "results": "355", "hashOfConfig": "263"}, {"size": 3093, "mtime": 1752597003081, "results": "356", "hashOfConfig": "263"}, {"size": 3074, "mtime": 1752590494491, "results": "357", "hashOfConfig": "263"}, {"size": 8405, "mtime": 1752641901054, "results": "358", "hashOfConfig": "263"}, {"size": 3756, "mtime": 1752565617212, "results": "359", "hashOfConfig": "263"}, {"size": 11563, "mtime": 1752671065324, "results": "360", "hashOfConfig": "263"}, {"size": 5285, "mtime": 1752573894027, "results": "361", "hashOfConfig": "263"}, {"size": 11061, "mtime": 1752574097974, "results": "362", "hashOfConfig": "263"}, {"size": 8218, "mtime": 1752575098604, "results": "363", "hashOfConfig": "263"}, {"size": 3313, "mtime": 1752590527244, "results": "364", "hashOfConfig": "263"}, {"size": 8917, "mtime": 1752670682659, "results": "365", "hashOfConfig": "263"}, {"size": 10969, "mtime": 1752670728274, "results": "366", "hashOfConfig": "263"}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "poe9py", {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "14ofb3m", {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "qkekr7", {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15319ot", {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "787", "messages": "788", "suppressedMessages": "789", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "790", "messages": "791", "suppressedMessages": "792", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "793", "messages": "794", "suppressedMessages": "795", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "796", "messages": "797", "suppressedMessages": "798", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "799", "messages": "800", "suppressedMessages": "801", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "802", "messages": "803", "suppressedMessages": "804", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "805", "messages": "806", "suppressedMessages": "807", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "808", "messages": "809", "suppressedMessages": "810", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "811", "messages": "812", "suppressedMessages": "813", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "814", "messages": "815", "suppressedMessages": "816", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "817", "messages": "818", "suppressedMessages": "819", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "820", "messages": "821", "suppressedMessages": "822", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "823", "messages": "824", "suppressedMessages": "825", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "826", "messages": "827", "suppressedMessages": "828", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "829", "messages": "830", "suppressedMessages": "831", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "832", "messages": "833", "suppressedMessages": "834", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "835", "messages": "836", "suppressedMessages": "837", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "838", "messages": "839", "suppressedMessages": "840", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "841", "messages": "842", "suppressedMessages": "843", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "844", "messages": "845", "suppressedMessages": "846", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "847", "messages": "848", "suppressedMessages": "849", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "850", "messages": "851", "suppressedMessages": "852", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "853", "messages": "854", "suppressedMessages": "855", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "856", "messages": "857", "suppressedMessages": "858", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "859", "messages": "860", "suppressedMessages": "861", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "862", "messages": "863", "suppressedMessages": "864", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "865", "messages": "866", "suppressedMessages": "867", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "868", "messages": "869", "suppressedMessages": "870", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "871", "messages": "872", "suppressedMessages": "873", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "874", "messages": "875", "suppressedMessages": "876", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "877", "messages": "878", "suppressedMessages": "879", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "880", "messages": "881", "suppressedMessages": "882", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "883", "messages": "884", "suppressedMessages": "885", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "886", "messages": "887", "suppressedMessages": "888", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "889", "messages": "890", "suppressedMessages": "891", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "892", "messages": "893", "suppressedMessages": "894", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "895", "messages": "896", "suppressedMessages": "897", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "898", "messages": "899", "suppressedMessages": "900", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "901", "messages": "902", "suppressedMessages": "903", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "904", "messages": "905", "suppressedMessages": "906", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "907", "messages": "908", "suppressedMessages": "909", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Desktop/MaBourseWebsite/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Scholarships.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/pages/Home.tsx", ["910"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Footer.tsx", ["911", "912", "913"], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/About.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Home.tsx", ["914"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Contact.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/pages/Scholarships.tsx", ["915"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Scholarships.tsx", ["916", "917"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Dashboard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Login.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/layout/Footer.tsx", ["918", "919", "920"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Sidebar.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipForm.tsx", ["921"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Messages.tsx", ["922", "923"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/AdminDashboard.tsx", ["924", "925", "926"], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/MessagesManager.tsx", [], ["927"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/ScholarshipsManager.tsx", ["928"], ["929"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/NewsletterManager.tsx", [], ["930"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/AdminProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorVerification.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/TwoFactorSetup.tsx", [], ["931"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AnalyticsDashboard.tsx", [], ["932"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/EmailNotificationSettings.tsx", ["933"], ["934"], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/apiConfig.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsite copy 2/src/admin/components/AdminLoginTester.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/App.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx", ["935", "936"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/About.tsx", ["937", "938"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/NotFound.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Contact.tsx", ["939", "940", "941"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/ScholarshipContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/context/LanguageContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Layout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/ErrorBoundary.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/NewsletterManager.tsx", ["942", "943"], ["944"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AdminLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Settings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminManagement.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminDashboard.tsx", ["945", "946"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ResetPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/ForgotPassword.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/EmailNotifications.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/TwoFactorSettings.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/Analytics.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipGrid.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/scholarships/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/fr.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Footer.tsx", ["947", "948", "949", "950"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/en.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/translations/ar.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/Header.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Loading.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/EmailNotificationSettings.tsx", [], ["951"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/TwoFactorSetup.tsx", [], ["952"], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/AnalyticsDashboard.tsx", ["953"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/LanguageSwitcher.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ContactForm.tsx", ["954"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/Modal.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSubscription.tsx", ["955"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SectionHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/scholarshipService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/icons/index.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateFormatter.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FundingSourceSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/HeroSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UniversityOrganizationSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/GovernmentScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/NewsletterSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/LatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StudyLevelCategoriesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedHome.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedStudyLevelSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedHeroSection.tsx", ["956"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedLatestScholarshipsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedFundingSourcesSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/TestimonialsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedNewsletterSection.tsx", ["957", "958"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/EnhancedScholarshipCard.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/EnhancedScholarshipDetailPage.tsx", ["959", "960"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/slugify.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/envValidator.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dateUtils.ts", ["961"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/config/axiosConfig.ts", ["962"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/AccountRecovery.tsx", ["963"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/SecurityDashboard.tsx", ["964", "965", "966"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/TestPanel.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/api.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/hooks/useAdminApi.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/contexts/AuthContext.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/authService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProtectedRoute.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/AdminLogin.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/ScholarshipManager.tsx", ["967"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/admin/MessagesManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/components/ScholarshipForm.tsx", ["968"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Countries.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Guides.tsx", ["969"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Opportunities.tsx", ["970"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/CountryDetail.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/GuideManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/admin/pages/OpportunityManager.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/imageUtils.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/layout/EnhancedHeader.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/NavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/common/Dropdown.tsx", ["971", "972"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/navigation/MobileNavigationDropdown.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/ScholarshipsByLevel.tsx", ["973"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/OpportunitiesByType.tsx", ["974", "975"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/services/sidebarService.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalPageLayout.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx", ["976"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/utils/dataPrefetcher.ts", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/UndergraduatePage.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/MasterPage.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/PageEndSuggestions.tsx", ["977", "978"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/AdPlacement.tsx", ["979"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx", ["980", "981"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/SimplifiedSidebar.tsx", ["982"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardCountryPage.tsx", ["983", "984"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/filters/StandardizedFilters.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/DoctoratePage.tsx", [], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UnifiedSidebar.tsx", ["985"], [], "/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/CommentsSection.tsx", ["986", "987", "988"], [], {"ruleId": "989", "severity": 1, "message": "990", "line": 170, "column": 7, "nodeType": "991", "messageId": "992", "endLine": 170, "endColumn": 15}, {"ruleId": "993", "severity": 1, "message": "994", "line": 51, "column": 15, "nodeType": "995", "endLine": 51, "endColumn": 74}, {"ruleId": "993", "severity": 1, "message": "994", "line": 57, "column": 15, "nodeType": "995", "endLine": 57, "endColumn": 74}, {"ruleId": "993", "severity": 1, "message": "994", "line": 63, "column": 15, "nodeType": "995", "endLine": 63, "endColumn": 74}, {"ruleId": "989", "severity": 1, "message": "990", "line": 170, "column": 7, "nodeType": "991", "messageId": "992", "endLine": 170, "endColumn": 15}, {"ruleId": "996", "severity": 1, "message": "997", "line": 44, "column": 6, "nodeType": "998", "endLine": 44, "endColumn": 68, "suggestions": "999"}, {"ruleId": "989", "severity": 1, "message": "1000", "line": 3, "column": 8, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 19}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 31, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 31, "endColumn": 23}, {"ruleId": "993", "severity": 1, "message": "994", "line": 51, "column": 15, "nodeType": "995", "endLine": 51, "endColumn": 74}, {"ruleId": "993", "severity": 1, "message": "994", "line": 57, "column": 15, "nodeType": "995", "endLine": 57, "endColumn": 74}, {"ruleId": "993", "severity": 1, "message": "994", "line": 63, "column": 15, "nodeType": "995", "endLine": 63, "endColumn": 74}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 34, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 34, "endColumn": 23}, {"ruleId": "989", "severity": 1, "message": "1002", "line": 10, "column": 3, "nodeType": "991", "messageId": "992", "endLine": 10, "endColumn": 8}, {"ruleId": "989", "severity": 1, "message": "1003", "line": 15, "column": 24, "nodeType": "991", "messageId": "992", "endLine": 15, "endColumn": 43}, {"ruleId": "989", "severity": 1, "message": "1004", "line": 3, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 21}, {"ruleId": "989", "severity": 1, "message": "1005", "line": 44, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 44, "endColumn": 17}, {"ruleId": "1006", "severity": 1, "message": "1007", "line": 162, "column": 36, "nodeType": "1008", "messageId": "1009", "endLine": 162, "endColumn": 60}, {"ruleId": "996", "severity": 1, "message": "1010", "line": 37, "column": 6, "nodeType": "998", "endLine": 37, "endColumn": 42, "suggestions": "1011", "suppressions": "1012"}, {"ruleId": "989", "severity": 1, "message": "1013", "line": 236, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 236, "endColumn": 30}, {"ruleId": "996", "severity": 1, "message": "1010", "line": 64, "column": 6, "nodeType": "998", "endLine": 64, "endColumn": 61, "suggestions": "1014", "suppressions": "1015"}, {"ruleId": "996", "severity": 1, "message": "1010", "line": 29, "column": 6, "nodeType": "998", "endLine": 29, "endColumn": 31, "suggestions": "1016", "suppressions": "1017"}, {"ruleId": "996", "severity": 1, "message": "1018", "line": 25, "column": 6, "nodeType": "998", "endLine": 25, "endColumn": 8, "suggestions": "1019", "suppressions": "1020"}, {"ruleId": "996", "severity": 1, "message": "1021", "line": 177, "column": 6, "nodeType": "998", "endLine": 177, "endColumn": 8, "suggestions": "1022", "suppressions": "1023"}, {"ruleId": "989", "severity": 1, "message": "1004", "line": 3, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 21}, {"ruleId": "996", "severity": 1, "message": "1024", "line": 37, "column": 6, "nodeType": "998", "endLine": 37, "endColumn": 8, "suggestions": "1025", "suppressions": "1026"}, {"ruleId": "989", "severity": 1, "message": "1027", "line": 5, "column": 8, "nodeType": "991", "messageId": "992", "endLine": 5, "endColumn": 25}, {"ruleId": "996", "severity": 1, "message": "997", "line": 65, "column": 6, "nodeType": "998", "endLine": 65, "endColumn": 68, "suggestions": "1028"}, {"ruleId": "993", "severity": 1, "message": "994", "line": 135, "column": 21, "nodeType": "995", "endLine": 135, "endColumn": 96}, {"ruleId": "993", "severity": 1, "message": "994", "line": 141, "column": 21, "nodeType": "995", "endLine": 141, "endColumn": 96}, {"ruleId": "993", "severity": 1, "message": "994", "line": 108, "column": 21, "nodeType": "995", "endLine": 108, "endColumn": 94}, {"ruleId": "993", "severity": 1, "message": "994", "line": 114, "column": 21, "nodeType": "995", "endLine": 114, "endColumn": 94}, {"ruleId": "993", "severity": 1, "message": "994", "line": 120, "column": 21, "nodeType": "995", "endLine": 120, "endColumn": 94}, {"ruleId": "989", "severity": 1, "message": "1029", "line": 2, "column": 105, "nodeType": "991", "messageId": "992", "endLine": 2, "endColumn": 111}, {"ruleId": "989", "severity": 1, "message": "1030", "line": 3, "column": 111, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 126}, {"ruleId": "996", "severity": 1, "message": "1010", "line": 45, "column": 6, "nodeType": "998", "endLine": 45, "endColumn": 31, "suggestions": "1031", "suppressions": "1032"}, {"ruleId": "989", "severity": 1, "message": "1005", "line": 44, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 44, "endColumn": 17}, {"ruleId": "996", "severity": 1, "message": "1033", "line": 106, "column": 6, "nodeType": "998", "endLine": 106, "endColumn": 8, "suggestions": "1034"}, {"ruleId": "993", "severity": 1, "message": "994", "line": 117, "column": 17, "nodeType": "995", "endLine": 117, "endColumn": 131}, {"ruleId": "993", "severity": 1, "message": "994", "line": 125, "column": 17, "nodeType": "995", "endLine": 125, "endColumn": 131}, {"ruleId": "993", "severity": 1, "message": "994", "line": 133, "column": 17, "nodeType": "995", "endLine": 133, "endColumn": 131}, {"ruleId": "993", "severity": 1, "message": "994", "line": 141, "column": 17, "nodeType": "995", "endLine": 141, "endColumn": 131}, {"ruleId": "996", "severity": 1, "message": "1024", "line": 37, "column": 6, "nodeType": "998", "endLine": 37, "endColumn": 8, "suggestions": "1035", "suppressions": "1036"}, {"ruleId": "996", "severity": 1, "message": "1018", "line": 25, "column": 6, "nodeType": "998", "endLine": 25, "endColumn": 8, "suggestions": "1037", "suppressions": "1038"}, {"ruleId": "989", "severity": 1, "message": "1039", "line": 38, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 38, "endColumn": 12}, {"ruleId": "993", "severity": 1, "message": "994", "line": 101, "column": 26, "nodeType": "995", "endLine": 101, "endColumn": 87}, {"ruleId": "989", "severity": 1, "message": "1040", "line": 2, "column": 40, "nodeType": "991", "messageId": "992", "endLine": 2, "endColumn": 44}, {"ruleId": "996", "severity": 1, "message": "1041", "line": 38, "column": 6, "nodeType": "998", "endLine": 38, "endColumn": 8, "suggestions": "1042"}, {"ruleId": "993", "severity": 1, "message": "994", "line": 186, "column": 21, "nodeType": "995", "endLine": 186, "endColumn": 74}, {"ruleId": "993", "severity": 1, "message": "994", "line": 188, "column": 21, "nodeType": "995", "endLine": 188, "endColumn": 74}, {"ruleId": "989", "severity": 1, "message": "1040", "line": 4, "column": 30, "nodeType": "991", "messageId": "992", "endLine": 4, "endColumn": 34}, {"ruleId": "989", "severity": 1, "message": "1043", "line": 7, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 7, "endColumn": 16}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 172, "column": 1, "nodeType": "1046", "endLine": 179, "endColumn": 3}, {"ruleId": "989", "severity": 1, "message": "1043", "line": 2, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 2, "endColumn": 16}, {"ruleId": "989", "severity": 1, "message": "1047", "line": 5, "column": 16, "nodeType": "991", "messageId": "992", "endLine": 5, "endColumn": 20}, {"ruleId": "989", "severity": 1, "message": "1048", "line": 13, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 13, "endColumn": 20}, {"ruleId": "989", "severity": 1, "message": "1049", "line": 15, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 15, "endColumn": 21}, {"ruleId": "996", "severity": 1, "message": "1050", "line": 106, "column": 6, "nodeType": "998", "endLine": 106, "endColumn": 8, "suggestions": "1051"}, {"ruleId": "989", "severity": 1, "message": "1052", "line": 10, "column": 3, "nodeType": "991", "messageId": "992", "endLine": 10, "endColumn": 12}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 32, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 32, "endColumn": 23}, {"ruleId": "996", "severity": 1, "message": "1053", "line": 26, "column": 6, "nodeType": "998", "endLine": 26, "endColumn": 24, "suggestions": "1054"}, {"ruleId": "996", "severity": 1, "message": "1055", "line": 35, "column": 6, "nodeType": "998", "endLine": 35, "endColumn": 15, "suggestions": "1056"}, {"ruleId": "996", "severity": 1, "message": "1057", "line": 86, "column": 6, "nodeType": "998", "endLine": 86, "endColumn": 14, "suggestions": "1058"}, {"ruleId": "996", "severity": 1, "message": "1057", "line": 98, "column": 6, "nodeType": "998", "endLine": 98, "endColumn": 14, "suggestions": "1059"}, {"ruleId": "989", "severity": 1, "message": "1060", "line": 3, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 21}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 31, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 31, "endColumn": 23}, {"ruleId": "996", "severity": 1, "message": "1061", "line": 49, "column": 6, "nodeType": "998", "endLine": 49, "endColumn": 32, "suggestions": "1062"}, {"ruleId": "989", "severity": 1, "message": "1060", "line": 3, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 3, "endColumn": 21}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 31, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 31, "endColumn": 23}, {"ruleId": "996", "severity": 1, "message": "1063", "line": 37, "column": 6, "nodeType": "998", "endLine": 37, "endColumn": 47, "suggestions": "1064"}, {"ruleId": "989", "severity": 1, "message": "1065", "line": 59, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 59, "endColumn": 21}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 55, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 55, "endColumn": 23}, {"ruleId": "996", "severity": 1, "message": "997", "line": 129, "column": 6, "nodeType": "998", "endLine": 129, "endColumn": 37, "suggestions": "1066"}, {"ruleId": "996", "severity": 1, "message": "997", "line": 55, "column": 6, "nodeType": "998", "endLine": 55, "endColumn": 40, "suggestions": "1067"}, {"ruleId": "989", "severity": 1, "message": "1001", "line": 50, "column": 11, "nodeType": "991", "messageId": "992", "endLine": 50, "endColumn": 23}, {"ruleId": "996", "severity": 1, "message": "997", "line": 114, "column": 6, "nodeType": "998", "endLine": 114, "endColumn": 39, "suggestions": "1068"}, {"ruleId": "996", "severity": 1, "message": "997", "line": 57, "column": 6, "nodeType": "998", "endLine": 57, "endColumn": 40, "suggestions": "1069"}, {"ruleId": "989", "severity": 1, "message": "1070", "line": 27, "column": 10, "nodeType": "991", "messageId": "992", "endLine": 27, "endColumn": 20}, {"ruleId": "996", "severity": 1, "message": "1071", "line": 32, "column": 6, "nodeType": "998", "endLine": 32, "endColumn": 24, "suggestions": "1072"}, {"ruleId": "989", "severity": 1, "message": "1073", "line": 123, "column": 9, "nodeType": "991", "messageId": "992", "endLine": 123, "endColumn": 26}, "@typescript-eslint/no-unused-vars", "'features' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchScholarships'. Either include it or remove the dependency array.", "ArrayExpression", ["1074"], "'AdminLayout' is defined but never used.", "'translations' is assigned a value but never used.", "'Space' is defined but never used.", "'CheckCircleOutlined' is defined but never used.", "'useAdminApi' is defined but never used.", "'navigate' is assigned a value but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'id'.", "ArrowFunctionExpression", "unsafeRefs", "React Hook useEffect has a missing dependency: 'applyFilters'. Either include it or remove the dependency array.", ["1075"], ["1076"], "'sendEmailNotification' is assigned a value but never used.", ["1077"], ["1078"], ["1079"], ["1080"], "React Hook useEffect has a missing dependency: 'initializeSetup'. Either include it or remove the dependency array.", ["1081"], ["1082"], "React Hook useEffect has a missing dependency: 'fetchAnalyticsData'. Either include it or remove the dependency array.", ["1083"], ["1084"], "React Hook useEffect has a missing dependency: 'fetchSettings'. Either include it or remove the dependency array.", ["1085"], ["1086"], "'SimplifiedSidebar' is defined but never used.", ["1087"], "'Select' is defined but never used.", "'FilePdfOutlined' is defined but never used.", ["1088"], ["1089"], "React Hook useEffect has missing dependencies: 'adminInfo' and 'stats'. Either include them or remove the dependency array.", ["1090"], ["1091"], ["1092"], ["1093"], ["1094"], "'api' is assigned a value but never used.", "'Spin' is defined but never used.", "React Hook useEffect has a missing dependency: 'backgrounds.length'. Either include it or remove the dependency array.", ["1095"], "'getEnv' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'Text' is assigned a value but never used.", "'DateFormat' is defined but never used.", "'ApiResponse' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSecurityEvents'. Either include it or remove the dependency array.", ["1096"], "'XMarkIcon' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchGuides'. Either include it or remove the dependency array.", ["1097"], "React Hook useEffect has a missing dependency: 'fetchOpportunities'. Either include it or remove the dependency array.", ["1098"], "React Hook useEffect has a missing dependency: 'closeDropdown'. Either include it or remove the dependency array.", ["1099"], ["1100"], "'useLanguage' is defined but never used.", "React Hook useEffect has missing dependencies: 'fetchOpportunitiesByType' and 'fetchTypeStatistics'. Either include them or remove the dependency array.", ["1101"], "React Hook useEffect has a missing dependency: 'fetchSuggestions'. Either include it or remove the dependency array.", ["1102"], "'adSizeConfig' is assigned a value but never used.", ["1103"], ["1104"], ["1105"], ["1106"], "'replyingTo' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchComments'. Either include it or remove the dependency array.", ["1107"], "'handleSubmitReply' is assigned a value but never used.", {"desc": "1108", "fix": "1109"}, {"desc": "1110", "fix": "1111"}, {"kind": "1112", "justification": "1113"}, {"desc": "1114", "fix": "1115"}, {"kind": "1112", "justification": "1113"}, {"desc": "1116", "fix": "1117"}, {"kind": "1112", "justification": "1113"}, {"desc": "1118", "fix": "1119"}, {"kind": "1112", "justification": "1113"}, {"desc": "1120", "fix": "1121"}, {"kind": "1112", "justification": "1113"}, {"desc": "1122", "fix": "1123"}, {"kind": "1112", "justification": "1113"}, {"desc": "1108", "fix": "1124"}, {"desc": "1116", "fix": "1125"}, {"kind": "1112", "justification": "1113"}, {"desc": "1126", "fix": "1127"}, {"desc": "1122", "fix": "1128"}, {"kind": "1112", "justification": "1113"}, {"desc": "1118", "fix": "1129"}, {"kind": "1112", "justification": "1113"}, {"desc": "1130", "fix": "1131"}, {"desc": "1132", "fix": "1133"}, {"desc": "1134", "fix": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1138", "fix": "1139"}, {"desc": "1138", "fix": "1140"}, {"desc": "1141", "fix": "1142"}, {"desc": "1143", "fix": "1144"}, {"desc": "1145", "fix": "1146"}, {"desc": "1147", "fix": "1148"}, {"desc": "1149", "fix": "1150"}, {"desc": "1147", "fix": "1151"}, {"desc": "1152", "fix": "1153"}, "Update the dependencies array to be: [pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", {"range": "1154", "text": "1155"}, "Update the dependencies array to be: [applyFilters, messages, searchTerm, statusFilter]", {"range": "1156", "text": "1157"}, "directive", "", "Update the dependencies array to be: [scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", {"range": "1158", "text": "1159"}, "Update the dependencies array to be: [subscribers, searchTerm, applyFilters]", {"range": "1160", "text": "1161"}, "Update the dependencies array to be: [initializeSetup]", {"range": "1162", "text": "1163"}, "Update the dependencies array to be: [fetchAnalyticsData]", {"range": "1164", "text": "1165"}, "Update the dependencies array to be: [fetchSettings]", {"range": "1166", "text": "1167"}, {"range": "1168", "text": "1155"}, {"range": "1169", "text": "1161"}, "Update the dependencies array to be: [adminInfo, stats]", {"range": "1170", "text": "1171"}, {"range": "1172", "text": "1167"}, {"range": "1173", "text": "1163"}, "Update the dependencies array to be: [backgrounds.length]", {"range": "1174", "text": "1175"}, "Update the dependencies array to be: [fetchSecurityEvents]", {"range": "1176", "text": "1177"}, "Update the dependencies array to be: [fetchGuides, selectedCategory]", {"range": "1178", "text": "1179"}, "Update the dependencies array to be: [fetchOpportunities, filters]", {"range": "1180", "text": "1181"}, "Update the dependencies array to be: [closeDropdown, isOpen]", {"range": "1182", "text": "1183"}, {"range": "1184", "text": "1183"}, "Update the dependencies array to be: [decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", {"range": "1185", "text": "1186"}, "Update the dependencies array to be: [currentPageType, currentItem, excludeId, fetchSuggestions]", {"range": "1187", "text": "1188"}, "Update the dependencies array to be: [pagination.page, config.level, fetchScholarships]", {"range": "1189", "text": "1190"}, "Update the dependencies array to be: [config.currentItem, config.limit, fetchScholarships]", {"range": "1191", "text": "1192"}, "Update the dependencies array to be: [pagination.page, config.country, fetchScholarships]", {"range": "1193", "text": "1194"}, {"range": "1195", "text": "1192"}, "Update the dependencies array to be: [pageType, pageId, fetchComments]", {"range": "1196", "text": "1197"}, [1225, 1287], "[pagination.page, selectedLevel, selectedCountry, searchQuery, fetchScholarships]", [1158, 1194], "[applyFilters, messages, searchTerm, statusFilter]", [1966, 2021], "[scholarships, searchTerm, filterStatus, filterCountry, applyFilters]", [903, 928], "[subscribers, searchTerm, applyFilters]", [969, 971], "[initializeSetup]", [6083, 6085], "[fetchAnalyticsData]", [1255, 1257], "[fetchSettings]", [2024, 2086], [1914, 1939], [3435, 3437], "[adminInfo, stats]", [1201, 1203], [969, 971], [1326, 1328], "[backgrounds.length]", [3118, 3120], "[fetchSecurityEvents]", [734, 752], "[<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]", [934, 943], "[fetchOpportunities, filters]", [2324, 2332], "[closeDropdown, isOpen]", [2652, 2660], [1499, 1525], "[decodedType, currentPage, fetchOpportunitiesByType, fetchTypeStatistics]", [900, 941], "[currentPageType, currentItem, excludeId, fetchSuggestions]", [3581, 3612], "[pagination.page, config.level, fetchScholarships]", [1396, 1430], "[config.currentItem, config.limit, fetchScholarships]", [3147, 3180], "[pagination.page, config.country, fetchScholarships]", [1525, 1559], [937, 955], "[pageType, pageId, fetchComments]"]