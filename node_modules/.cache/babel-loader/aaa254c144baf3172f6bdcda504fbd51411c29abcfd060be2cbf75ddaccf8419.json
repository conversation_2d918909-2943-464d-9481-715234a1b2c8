{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport sidebarService from '../services/sidebarService';\nimport dataPrefetcher from '../utils/dataPrefetcher';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ProfessionalSidebar = ({\n  config,\n  className = ''\n}) => {\n  _s();\n  const [sidebarData, setSidebarData] = useState({\n    relatedItems: [],\n    latestItems: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const fetchSidebarData = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use the enhanced caching service\n      const data = await sidebarService.fetchSidebarData(config);\n      setSidebarData(data);\n\n      // If we got some data but not all, show partial loading\n      if (data.relatedItems.length === 0 && data.latestItems.length === 0) {\n        setError('Unable to load sidebar content');\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar data:', error);\n      setError('Failed to load sidebar content');\n\n      // Try to get fallback data from cache\n      try {\n        const fallbackData = await sidebarService.fetchSidebarData(config);\n        if (fallbackData.relatedItems.length > 0 || fallbackData.latestItems.length > 0) {\n          setSidebarData(fallbackData);\n          setError('Showing cached content');\n        }\n      } catch (fallbackError) {\n        console.error('Fallback also failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [config]);\n  useEffect(() => {\n    fetchSidebarData();\n  }, [fetchSidebarData]);\n  const getRelatedItemsTitle = () => {\n    switch (config.type) {\n      case 'countries':\n        return 'Autres Pays';\n      case 'levels':\n        return 'Autres Niveaux d\\'Études';\n      case 'opportunities':\n        return 'Autres Types d\\'Opportunités';\n      default:\n        return 'Éléments Connexes';\n    }\n  };\n  const getLatestItemsTitle = () => {\n    return config.type === 'opportunities' ? 'Dernières Opportunités' : 'Dernières Bourses';\n  };\n  const getRelatedItemLink = item => {\n    switch (config.type) {\n      case 'countries':\n        return `/countries/${encodeURIComponent(item.name)}`;\n      case 'levels':\n        return `/scholarships/level/${encodeURIComponent(item.name)}`;\n      case 'opportunities':\n        return `/opportunities/type/${encodeURIComponent(item.name)}`;\n      default:\n        return '#';\n    }\n  };\n  const getRelatedItemIcon = item => {\n    switch (config.type) {\n      case 'countries':\n        return sidebarService.getCountryFlag(item.name);\n      case 'levels':\n        return sidebarService.getLevelIcon(item.name);\n      case 'opportunities':\n        return sidebarService.getOpportunityIcon(item.name);\n      default:\n        return '📄';\n    }\n  };\n  const getLatestItemLink = item => {\n    if (config.type === 'opportunities') {\n      return `/opportunities/${item.id}`;\n    }\n    return `/scholarships/${item.id}`;\n  };\n  const renderErrorState = (message, onRetry) => /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center py-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"text-4xl mb-2\",\n      children: \"\\u26A0\\uFE0F\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 131,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-sm text-gray-600 mb-3\",\n      children: message\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 7\n    }, this), onRetry && /*#__PURE__*/_jsxDEV(\"button\", {\n      onClick: onRetry,\n      className: \"px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\",\n      children: \"R\\xE9essayer\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 130,\n    columnNumber: 5\n  }, this);\n  const renderRelatedItems = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-2\",\n        children: [...Array(8)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center p-3 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-gray-200 rounded mr-3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex-1\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-4 bg-gray-200 rounded w-3/4 mb-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-3 bg-gray-200 rounded w-1/2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 154,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 147,\n        columnNumber: 9\n      }, this);\n    }\n    if (error && sidebarData.relatedItems.length === 0) {\n      return renderErrorState(error, fetchSidebarData);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-1 max-h-80 overflow-y-auto custom-scrollbar\",\n      children: sidebarData.relatedItems.slice(0, 15).map((item, index) => /*#__PURE__*/_jsxDEV(Link, {\n        to: getRelatedItemLink(item),\n        className: \"flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm\",\n        onMouseEnter: () => {\n          // Prefetch data on hover for better UX\n          if (config.type === 'countries') {\n            dataPrefetcher.prefetchOnHover({\n              type: 'countries',\n              currentItem: item.name\n            });\n          }\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-2xl mr-3 group-hover:scale-110 transition-transform duration-300 filter group-hover:brightness-110\",\n            children: getRelatedItemIcon(item)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), ((item.openCount || 0) > 0 || (item.activeCount || 0) > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 min-w-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"font-medium text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200\",\n            children: item.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-xs text-gray-500 flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"w-1.5 h-1.5 bg-gray-400 rounded-full mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 19\n              }, this), item.totalCount || item.count, \" \", config.type === 'opportunities' ? 'opportunités' : 'bourses']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 194,\n              columnNumber: 17\n            }, this), (item.openCount || item.activeCount) && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex items-center text-green-600\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"w-1.5 h-1.5 bg-green-500 rounded-full mr-1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 200,\n                columnNumber: 21\n              }, this), item.openCount || item.activeCount, \" \", config.type === 'opportunities' ? 'actives' : 'ouvertes']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 199,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 193,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-4 h-4 text-blue-500\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M9 5l7 7-7 7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 208,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 207,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this);\n  };\n  const renderLatestItems = () => {\n    if (loading) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [...Array(5)].map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"p-3 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-4 bg-gray-200 rounded w-full mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-3 bg-gray-200 rounded w-2/3 mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"h-6 bg-gray-200 rounded w-16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 223,\n            columnNumber: 15\n          }, this)\n        }, index, false, {\n          fileName: _jsxFileName,\n          lineNumber: 222,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 220,\n        columnNumber: 9\n      }, this);\n    }\n    if (error && sidebarData.latestItems.length === 0) {\n      return renderErrorState(error, fetchSidebarData);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-3\",\n      children: sidebarData.latestItems.slice(0, 5).map((item, index) => /*#__PURE__*/_jsxDEV(Link, {\n        to: getLatestItemLink(item),\n        className: \"block p-4 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: item.thumbnail ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"relative\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: item.thumbnail,\n                alt: item.title || item.name,\n                className: \"w-12 h-12 rounded-lg object-cover border border-gray-200\",\n                onError: e => {\n                  const target = e.target;\n                  target.src = '/assets/default-scholarship.jpg';\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 251,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${(config.type === 'opportunities' ? item.isActive : item.isOpen) ? 'bg-green-500' : 'bg-gray-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 250,\n              columnNumber: 19\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border border-gray-200\",\n              children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"w-6 h-6 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 269,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 268,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: `absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${(config.type === 'opportunities' ? item.isActive : item.isOpen) ? 'bg-green-500' : 'bg-gray-400'}`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 267,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 248,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"font-medium text-gray-900 text-sm mb-2 line-clamp-2 group-hover:text-blue-700 transition-colors duration-200\",\n              children: item.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 280,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center text-xs text-gray-500 mb-2 space-x-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-3 h-3 mr-1\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: [/*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 286,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 287,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 21\n                }, this), config.type === 'opportunities' ? item.organization : item.country]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-gray-300\",\n                children: \"\\u2022\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-3 h-3 mr-1\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 294,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 293,\n                  columnNumber: 21\n                }, this), config.type === 'opportunities' ? item.type : item.level]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 292,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${(config.type === 'opportunities' ? item.isActive : item.isOpen) ? 'bg-green-100 text-green-800 border border-green-200' : 'bg-gray-100 text-gray-600 border border-gray-200'}`,\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `w-1.5 h-1.5 rounded-full mr-1.5 ${(config.type === 'opportunities' ? item.isActive : item.isOpen) ? 'bg-green-500' : 'bg-gray-400'}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 21\n                }, this), (config.type === 'opportunities' ? item.isActive : item.isOpen) ? config.type === 'opportunities' ? 'Active' : 'Ouverte' : config.type === 'opportunities' ? 'Inactive' : 'Fermée']\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"opacity-0 group-hover:opacity-100 transition-opacity duration-200\",\n                children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-4 h-4 text-blue-500\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 317,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 315,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 279,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 246,\n          columnNumber: 13\n        }, this)\n      }, item.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 241,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 239,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `lg:w-1/3 space-y-6 ${className}`,\n      children: [error && (sidebarData.relatedItems.length > 0 || sidebarData.latestItems.length > 0) && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 shadow-sm\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-5 h-5 text-yellow-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"ml-3 flex-1\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm font-medium text-yellow-800\",\n              children: error\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 343,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 342,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: fetchSidebarData,\n            className: \"ml-3 text-xs font-medium text-yellow-700 hover:text-yellow-900 bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded-full transition-colors duration-200\",\n            children: \"Actualiser\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 345,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 336,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 335,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-blue-600\",\n                children: \"\\uD83D\\uDD17\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 360,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-1\",\n              children: getRelatedItemsTitle()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 362,\n              columnNumber: 15\n            }, this), !loading && sidebarData.relatedItems.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-blue-700 bg-blue-100 px-2.5 py-1 rounded-full\",\n              children: sidebarData.relatedItems.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 364,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 358,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 357,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: renderRelatedItems()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 356,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100\",\n          children: /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-lg font-semibold text-gray-900 flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-green-600\",\n                children: \"\\u2B50\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 380,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 379,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"flex-1\",\n              children: getLatestItemsTitle()\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 382,\n              columnNumber: 15\n            }, this), !loading && sidebarData.latestItems.length > 0 && /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-xs font-medium text-green-700 bg-green-100 px-2.5 py-1 rounded-full\",\n              children: sidebarData.latestItems.length\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 384,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 378,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 377,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"p-6\",\n          children: renderLatestItems()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n};\n_s(ProfessionalSidebar, \"L6wBLqpXiIASwn7qmQnpEskBTIk=\");\n_c = ProfessionalSidebar;\nexport default ProfessionalSidebar;\nvar _c;\n$RefreshReg$(_c, \"ProfessionalSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "sidebarService", "dataPrefetcher", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ProfessionalSidebar", "config", "className", "_s", "sidebarData", "setSidebarData", "relatedItems", "latestItems", "loading", "setLoading", "error", "setError", "fetchSidebarData", "useCallback", "data", "length", "console", "fallbackD<PERSON>", "fallback<PERSON><PERSON>r", "getRelatedItemsTitle", "type", "getLatestItemsTitle", "getRelatedItemLink", "item", "encodeURIComponent", "name", "getRelatedItemIcon", "getCountryFlag", "getLevelIcon", "getOpportunityIcon", "getLatestItemLink", "id", "renderErrorState", "message", "onRetry", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "renderRelatedItems", "Array", "map", "_", "index", "slice", "to", "onMouseEnter", "prefetchOnHover", "currentItem", "openCount", "activeCount", "totalCount", "count", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "renderLatestItems", "thumbnail", "src", "alt", "title", "onError", "e", "target", "isActive", "isOpen", "organization", "country", "level", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/ProfessionalSidebar.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { useLanguage } from '../context/LanguageContext';\nimport sidebarService, { SidebarConfig, SidebarData } from '../services/sidebarService';\nimport dataPrefetcher from '../utils/dataPrefetcher';\n\ninterface ProfessionalSidebarProps {\n  config: SidebarConfig;\n  className?: string;\n}\n\ninterface SidebarItem {\n  id?: number;\n  name: string;\n  title?: string;\n  thumbnail?: string;\n  totalCount?: number;\n  openCount?: number;\n  activeCount?: number;\n  count?: number;\n  slug?: string;\n  country?: string;\n  level?: string;\n  type?: string;\n  organization?: string;\n  deadline?: string;\n  isOpen?: boolean;\n  isActive?: boolean;\n  isExpired?: boolean;\n  daysRemaining?: number;\n}\n\nconst ProfessionalSidebar: React.FC<ProfessionalSidebarProps> = ({ config, className = '' }) => {\n  const [sidebarData, setSidebarData] = useState<SidebarData>({\n    relatedItems: [],\n    latestItems: []\n  });\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchSidebarData = React.useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      // Use the enhanced caching service\n      const data = await sidebarService.fetchSidebarData(config);\n      setSidebarData(data);\n\n      // If we got some data but not all, show partial loading\n      if (data.relatedItems.length === 0 && data.latestItems.length === 0) {\n        setError('Unable to load sidebar content');\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar data:', error);\n      setError('Failed to load sidebar content');\n\n      // Try to get fallback data from cache\n      try {\n        const fallbackData = await sidebarService.fetchSidebarData(config);\n        if (fallbackData.relatedItems.length > 0 || fallbackData.latestItems.length > 0) {\n          setSidebarData(fallbackData);\n          setError('Showing cached content');\n        }\n      } catch (fallbackError) {\n        console.error('Fallback also failed:', fallbackError);\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [config]);\n\n  useEffect(() => {\n    fetchSidebarData();\n  }, [fetchSidebarData]);\n\n\n\n  const getRelatedItemsTitle = () => {\n    switch (config.type) {\n      case 'countries':\n        return 'Autres Pays';\n      case 'levels':\n        return 'Autres Niveaux d\\'Études';\n      case 'opportunities':\n        return 'Autres Types d\\'Opportunités';\n      default:\n        return 'Éléments Connexes';\n    }\n  };\n\n  const getLatestItemsTitle = () => {\n    return config.type === 'opportunities' ? 'Dernières Opportunités' : 'Dernières Bourses';\n  };\n\n  const getRelatedItemLink = (item: SidebarItem) => {\n    switch (config.type) {\n      case 'countries':\n        return `/countries/${encodeURIComponent(item.name)}`;\n      case 'levels':\n        return `/scholarships/level/${encodeURIComponent(item.name)}`;\n      case 'opportunities':\n        return `/opportunities/type/${encodeURIComponent(item.name)}`;\n      default:\n        return '#';\n    }\n  };\n\n  const getRelatedItemIcon = (item: SidebarItem) => {\n    switch (config.type) {\n      case 'countries':\n        return sidebarService.getCountryFlag(item.name);\n      case 'levels':\n        return sidebarService.getLevelIcon(item.name);\n      case 'opportunities':\n        return sidebarService.getOpportunityIcon(item.name);\n      default:\n        return '📄';\n    }\n  };\n\n  const getLatestItemLink = (item: SidebarItem) => {\n    if (config.type === 'opportunities') {\n      return `/opportunities/${item.id}`;\n    }\n    return `/scholarships/${item.id}`;\n  };\n\n  const renderErrorState = (message: string, onRetry?: () => void) => (\n    <div className=\"text-center py-8\">\n      <div className=\"text-4xl mb-2\">⚠️</div>\n      <p className=\"text-sm text-gray-600 mb-3\">{message}</p>\n      {onRetry && (\n        <button\n          onClick={onRetry}\n          className=\"px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors\"\n        >\n          Réessayer\n        </button>\n      )}\n    </div>\n  );\n\n  const renderRelatedItems = () => {\n    if (loading) {\n      return (\n        <div className=\"space-y-2\">\n          {[...Array(8)].map((_, index) => (\n            <div key={index} className=\"animate-pulse\">\n              <div className=\"flex items-center p-3 rounded-lg\">\n                <div className=\"w-8 h-8 bg-gray-200 rounded mr-3\"></div>\n                <div className=\"flex-1\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-1\"></div>\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      );\n    }\n\n    if (error && sidebarData.relatedItems.length === 0) {\n      return renderErrorState(error, fetchSidebarData);\n    }\n\n    return (\n      <div className=\"space-y-1 max-h-80 overflow-y-auto custom-scrollbar\">\n        {sidebarData.relatedItems.slice(0, 15).map((item: SidebarItem, index) => (\n          <Link\n            key={index}\n            to={getRelatedItemLink(item)}\n            className=\"flex items-center p-3 rounded-xl hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm\"\n            onMouseEnter={() => {\n              // Prefetch data on hover for better UX\n              if (config.type === 'countries') {\n                dataPrefetcher.prefetchOnHover({ type: 'countries', currentItem: item.name });\n              }\n            }}\n          >\n            <div className=\"relative\">\n              <span className=\"text-2xl mr-3 group-hover:scale-110 transition-transform duration-300 filter group-hover:brightness-110\">\n                {getRelatedItemIcon(item)}\n              </span>\n              {((item.openCount || 0) > 0 || (item.activeCount || 0) > 0) && (\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white\"></div>\n              )}\n            </div>\n            <div className=\"flex-1 min-w-0\">\n              <div className=\"font-medium text-gray-900 truncate group-hover:text-blue-700 transition-colors duration-200\">\n                {item.name}\n              </div>\n              <div className=\"text-xs text-gray-500 flex items-center space-x-2\">\n                <span className=\"flex items-center\">\n                  <span className=\"w-1.5 h-1.5 bg-gray-400 rounded-full mr-1\"></span>\n                  {item.totalCount || item.count} {config.type === 'opportunities' ? 'opportunités' : 'bourses'}\n                </span>\n                {(item.openCount || item.activeCount) && (\n                  <span className=\"flex items-center text-green-600\">\n                    <span className=\"w-1.5 h-1.5 bg-green-500 rounded-full mr-1\"></span>\n                    {item.openCount || item.activeCount} {config.type === 'opportunities' ? 'actives' : 'ouvertes'}\n                  </span>\n                )}\n              </div>\n            </div>\n            <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n              <svg className=\"w-4 h-4 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n              </svg>\n            </div>\n          </Link>\n        ))}\n      </div>\n    );\n  };\n\n  const renderLatestItems = () => {\n    if (loading) {\n      return (\n        <div className=\"space-y-4\">\n          {[...Array(5)].map((_, index) => (\n            <div key={index} className=\"animate-pulse\">\n              <div className=\"p-3 rounded-lg\">\n                <div className=\"h-4 bg-gray-200 rounded w-full mb-2\"></div>\n                <div className=\"h-3 bg-gray-200 rounded w-2/3 mb-2\"></div>\n                <div className=\"h-6 bg-gray-200 rounded w-16\"></div>\n              </div>\n            </div>\n          ))}\n        </div>\n      );\n    }\n\n    if (error && sidebarData.latestItems.length === 0) {\n      return renderErrorState(error, fetchSidebarData);\n    }\n\n    return (\n      <div className=\"space-y-3\">\n        {sidebarData.latestItems.slice(0, 5).map((item: SidebarItem, index) => (\n          <Link\n            key={item.id}\n            to={getLatestItemLink(item)}\n            className=\"block p-4 rounded-xl hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50 transition-all duration-300 group border border-transparent hover:border-blue-100 hover:shadow-sm\"\n          >\n            <div className=\"flex items-start space-x-3\">\n              {/* Thumbnail */}\n              <div className=\"flex-shrink-0\">\n                {item.thumbnail ? (\n                  <div className=\"relative\">\n                    <img\n                      src={item.thumbnail}\n                      alt={item.title || item.name}\n                      className=\"w-12 h-12 rounded-lg object-cover border border-gray-200\"\n                      onError={(e) => {\n                        const target = e.target as HTMLImageElement;\n                        target.src = '/assets/default-scholarship.jpg';\n                      }}\n                    />\n                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${\n                      (config.type === 'opportunities' ? item.isActive : item.isOpen)\n                        ? 'bg-green-500'\n                        : 'bg-gray-400'\n                    }`}></div>\n                  </div>\n                ) : (\n                  <div className=\"w-12 h-12 rounded-lg bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center border border-gray-200\">\n                    <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                    </svg>\n                    <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full border-2 border-white ${\n                      (config.type === 'opportunities' ? item.isActive : item.isOpen)\n                        ? 'bg-green-500'\n                        : 'bg-gray-400'\n                    }`}></div>\n                  </div>\n                )}\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h4 className=\"font-medium text-gray-900 text-sm mb-2 line-clamp-2 group-hover:text-blue-700 transition-colors duration-200\">\n                  {item.title}\n                </h4>\n                <div className=\"flex items-center text-xs text-gray-500 mb-2 space-x-2\">\n                  <span className=\"flex items-center\">\n                    <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                    </svg>\n                    {config.type === 'opportunities' ? item.organization : item.country}\n                  </span>\n                  <span className=\"text-gray-300\">•</span>\n                  <span className=\"flex items-center\">\n                    <svg className=\"w-3 h-3 mr-1\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n                    </svg>\n                    {config.type === 'opportunities' ? item.type : item.level}\n                  </span>\n                </div>\n                <div className=\"flex items-center justify-between\">\n                  <div className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${\n                    (config.type === 'opportunities' ? item.isActive : item.isOpen)\n                      ? 'bg-green-100 text-green-800 border border-green-200'\n                      : 'bg-gray-100 text-gray-600 border border-gray-200'\n                  }`}>\n                    <span className={`w-1.5 h-1.5 rounded-full mr-1.5 ${\n                      (config.type === 'opportunities' ? item.isActive : item.isOpen)\n                        ? 'bg-green-500'\n                        : 'bg-gray-400'\n                    }`}></span>\n                    {(config.type === 'opportunities' ? item.isActive : item.isOpen)\n                      ? (config.type === 'opportunities' ? 'Active' : 'Ouverte')\n                      : (config.type === 'opportunities' ? 'Inactive' : 'Fermée')\n                    }\n                  </div>\n                  <div className=\"opacity-0 group-hover:opacity-100 transition-opacity duration-200\">\n                    <svg className=\"w-4 h-4 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                    </svg>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </Link>\n        ))}\n      </div>\n    );\n  };\n\n  return (\n    <>\n\n      <div className={`lg:w-1/3 space-y-6 ${className}`}>\n        {/* Error Banner */}\n        {error && (sidebarData.relatedItems.length > 0 || sidebarData.latestItems.length > 0) && (\n          <div className=\"bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-xl p-4 shadow-sm\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"w-5 h-5 text-yellow-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n                </svg>\n              </div>\n              <div className=\"ml-3 flex-1\">\n                <span className=\"text-sm font-medium text-yellow-800\">{error}</span>\n              </div>\n              <button\n                onClick={fetchSidebarData}\n                className=\"ml-3 text-xs font-medium text-yellow-700 hover:text-yellow-900 bg-yellow-100 hover:bg-yellow-200 px-3 py-1 rounded-full transition-colors duration-200\"\n              >\n                Actualiser\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Related Items Section */}\n        <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n              <div className=\"w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3\">\n                <span className=\"text-blue-600\">🔗</span>\n              </div>\n              <span className=\"flex-1\">{getRelatedItemsTitle()}</span>\n              {!loading && sidebarData.relatedItems.length > 0 && (\n                <span className=\"text-xs font-medium text-blue-700 bg-blue-100 px-2.5 py-1 rounded-full\">\n                  {sidebarData.relatedItems.length}\n                </span>\n              )}\n            </h3>\n          </div>\n          <div className=\"p-6\">\n            {renderRelatedItems()}\n          </div>\n        </div>\n\n        {/* Latest Items Section */}\n        <div className=\"bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden\">\n          <div className=\"bg-gradient-to-r from-green-50 to-emerald-50 px-6 py-4 border-b border-gray-100\">\n            <h3 className=\"text-lg font-semibold text-gray-900 flex items-center\">\n              <div className=\"w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3\">\n                <span className=\"text-green-600\">⭐</span>\n              </div>\n              <span className=\"flex-1\">{getLatestItemsTitle()}</span>\n              {!loading && sidebarData.latestItems.length > 0 && (\n                <span className=\"text-xs font-medium text-green-700 bg-green-100 px-2.5 py-1 rounded-full\">\n                  {sidebarData.latestItems.length}\n                </span>\n              )}\n            </h3>\n          </div>\n          <div className=\"p-6\">\n            {renderLatestItems()}\n          </div>\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default ProfessionalSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAEvC,OAAOC,cAAc,MAAsC,4BAA4B;AACvF,OAAOC,cAAc,MAAM,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA4BrD,MAAMC,mBAAuD,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EAC9F,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGd,QAAQ,CAAc;IAC1De,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE;EACf,CAAC,CAAC;EACF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACmB,KAAK,EAAEC,QAAQ,CAAC,GAAGpB,QAAQ,CAAgB,IAAI,CAAC;EAEvD,MAAMqB,gBAAgB,GAAGtB,KAAK,CAACuB,WAAW,CAAC,YAAY;IACrD,IAAI;MACFJ,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;;MAEd;MACA,MAAMG,IAAI,GAAG,MAAMpB,cAAc,CAACkB,gBAAgB,CAACX,MAAM,CAAC;MAC1DI,cAAc,CAACS,IAAI,CAAC;;MAEpB;MACA,IAAIA,IAAI,CAACR,YAAY,CAACS,MAAM,KAAK,CAAC,IAAID,IAAI,CAACP,WAAW,CAACQ,MAAM,KAAK,CAAC,EAAE;QACnEJ,QAAQ,CAAC,gCAAgC,CAAC;MAC5C;IACF,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdM,OAAO,CAACN,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAAC,gCAAgC,CAAC;;MAE1C;MACA,IAAI;QACF,MAAMM,YAAY,GAAG,MAAMvB,cAAc,CAACkB,gBAAgB,CAACX,MAAM,CAAC;QAClE,IAAIgB,YAAY,CAACX,YAAY,CAACS,MAAM,GAAG,CAAC,IAAIE,YAAY,CAACV,WAAW,CAACQ,MAAM,GAAG,CAAC,EAAE;UAC/EV,cAAc,CAACY,YAAY,CAAC;UAC5BN,QAAQ,CAAC,wBAAwB,CAAC;QACpC;MACF,CAAC,CAAC,OAAOO,aAAa,EAAE;QACtBF,OAAO,CAACN,KAAK,CAAC,uBAAuB,EAAEQ,aAAa,CAAC;MACvD;IACF,CAAC,SAAS;MACRT,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC,EAAE,CAACR,MAAM,CAAC,CAAC;EAEZT,SAAS,CAAC,MAAM;IACdoB,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACA,gBAAgB,CAAC,CAAC;EAItB,MAAMO,oBAAoB,GAAGA,CAAA,KAAM;IACjC,QAAQlB,MAAM,CAACmB,IAAI;MACjB,KAAK,WAAW;QACd,OAAO,aAAa;MACtB,KAAK,QAAQ;QACX,OAAO,0BAA0B;MACnC,KAAK,eAAe;QAClB,OAAO,8BAA8B;MACvC;QACE,OAAO,mBAAmB;IAC9B;EACF,CAAC;EAED,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;IAChC,OAAOpB,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAG,wBAAwB,GAAG,mBAAmB;EACzF,CAAC;EAED,MAAME,kBAAkB,GAAIC,IAAiB,IAAK;IAChD,QAAQtB,MAAM,CAACmB,IAAI;MACjB,KAAK,WAAW;QACd,OAAO,cAAcI,kBAAkB,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MACtD,KAAK,QAAQ;QACX,OAAO,uBAAuBD,kBAAkB,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MAC/D,KAAK,eAAe;QAClB,OAAO,uBAAuBD,kBAAkB,CAACD,IAAI,CAACE,IAAI,CAAC,EAAE;MAC/D;QACE,OAAO,GAAG;IACd;EACF,CAAC;EAED,MAAMC,kBAAkB,GAAIH,IAAiB,IAAK;IAChD,QAAQtB,MAAM,CAACmB,IAAI;MACjB,KAAK,WAAW;QACd,OAAO1B,cAAc,CAACiC,cAAc,CAACJ,IAAI,CAACE,IAAI,CAAC;MACjD,KAAK,QAAQ;QACX,OAAO/B,cAAc,CAACkC,YAAY,CAACL,IAAI,CAACE,IAAI,CAAC;MAC/C,KAAK,eAAe;QAClB,OAAO/B,cAAc,CAACmC,kBAAkB,CAACN,IAAI,CAACE,IAAI,CAAC;MACrD;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAIP,IAAiB,IAAK;IAC/C,IAAItB,MAAM,CAACmB,IAAI,KAAK,eAAe,EAAE;MACnC,OAAO,kBAAkBG,IAAI,CAACQ,EAAE,EAAE;IACpC;IACA,OAAO,iBAAiBR,IAAI,CAACQ,EAAE,EAAE;EACnC,CAAC;EAED,MAAMC,gBAAgB,GAAGA,CAACC,OAAe,EAAEC,OAAoB,kBAC7DrC,OAAA;IAAKK,SAAS,EAAC,kBAAkB;IAAAiC,QAAA,gBAC/BtC,OAAA;MAAKK,SAAS,EAAC,eAAe;MAAAiC,QAAA,EAAC;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC,eACvC1C,OAAA;MAAGK,SAAS,EAAC,4BAA4B;MAAAiC,QAAA,EAAEF;IAAO;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,EACtDL,OAAO,iBACNrC,OAAA;MACE2C,OAAO,EAAEN,OAAQ;MACjBhC,SAAS,EAAC,yFAAyF;MAAAiC,QAAA,EACpG;IAED;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CACT;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CACN;EAED,MAAME,kBAAkB,GAAGA,CAAA,KAAM;IAC/B,IAAIjC,OAAO,EAAE;MACX,oBACEX,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAiC,QAAA,EACvB,CAAC,GAAGO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1BhD,OAAA;UAAiBK,SAAS,EAAC,eAAe;UAAAiC,QAAA,eACxCtC,OAAA;YAAKK,SAAS,EAAC,kCAAkC;YAAAiC,QAAA,gBAC/CtC,OAAA;cAAKK,SAAS,EAAC;YAAkC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxD1C,OAAA;cAAKK,SAAS,EAAC,QAAQ;cAAAiC,QAAA,gBACrBtC,OAAA;gBAAKK,SAAS,EAAC;cAAoC;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC1D1C,OAAA;gBAAKK,SAAS,EAAC;cAA+B;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAPEM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,IAAI7B,KAAK,IAAIN,WAAW,CAACE,YAAY,CAACS,MAAM,KAAK,CAAC,EAAE;MAClD,OAAOiB,gBAAgB,CAACtB,KAAK,EAAEE,gBAAgB,CAAC;IAClD;IAEA,oBACEf,OAAA;MAAKK,SAAS,EAAC,qDAAqD;MAAAiC,QAAA,EACjE/B,WAAW,CAACE,YAAY,CAACwC,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACH,GAAG,CAAC,CAACpB,IAAiB,EAAEsB,KAAK,kBAClEhD,OAAA,CAACJ,IAAI;QAEHsD,EAAE,EAAEzB,kBAAkB,CAACC,IAAI,CAAE;QAC7BrB,SAAS,EAAC,iMAAiM;QAC3M8C,YAAY,EAAEA,CAAA,KAAM;UAClB;UACA,IAAI/C,MAAM,CAACmB,IAAI,KAAK,WAAW,EAAE;YAC/BzB,cAAc,CAACsD,eAAe,CAAC;cAAE7B,IAAI,EAAE,WAAW;cAAE8B,WAAW,EAAE3B,IAAI,CAACE;YAAK,CAAC,CAAC;UAC/E;QACF,CAAE;QAAAU,QAAA,gBAEFtC,OAAA;UAAKK,SAAS,EAAC,UAAU;UAAAiC,QAAA,gBACvBtC,OAAA;YAAMK,SAAS,EAAC,yGAAyG;YAAAiC,QAAA,EACtHT,kBAAkB,CAACH,IAAI;UAAC;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC,EACN,CAAC,CAAChB,IAAI,CAAC4B,SAAS,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC5B,IAAI,CAAC6B,WAAW,IAAI,CAAC,IAAI,CAAC,kBACxDvD,OAAA;YAAKK,SAAS,EAAC;UAAkF;YAAAkC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CACxG;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eACN1C,OAAA;UAAKK,SAAS,EAAC,gBAAgB;UAAAiC,QAAA,gBAC7BtC,OAAA;YAAKK,SAAS,EAAC,6FAA6F;YAAAiC,QAAA,EACzGZ,IAAI,CAACE;UAAI;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC,eACN1C,OAAA;YAAKK,SAAS,EAAC,mDAAmD;YAAAiC,QAAA,gBAChEtC,OAAA;cAAMK,SAAS,EAAC,mBAAmB;cAAAiC,QAAA,gBACjCtC,OAAA;gBAAMK,SAAS,EAAC;cAA2C;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EAClEhB,IAAI,CAAC8B,UAAU,IAAI9B,IAAI,CAAC+B,KAAK,EAAC,GAAC,EAACrD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAG,cAAc,GAAG,SAAS;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzF,CAAC,EACN,CAAChB,IAAI,CAAC4B,SAAS,IAAI5B,IAAI,CAAC6B,WAAW,kBAClCvD,OAAA;cAAMK,SAAS,EAAC,kCAAkC;cAAAiC,QAAA,gBAChDtC,OAAA;gBAAMK,SAAS,EAAC;cAA4C;gBAAAkC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC,EACnEhB,IAAI,CAAC4B,SAAS,IAAI5B,IAAI,CAAC6B,WAAW,EAAC,GAAC,EAACnD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAG,SAAS,GAAG,UAAU;YAAA;cAAAgB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1F,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN1C,OAAA;UAAKK,SAAS,EAAC,mEAAmE;UAAAiC,QAAA,eAChFtC,OAAA;YAAKK,SAAS,EAAC,uBAAuB;YAACqD,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAtB,QAAA,eAC1FtC,OAAA;cAAM6D,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAc;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA,GAvCDM,KAAK;QAAAT,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAwCN,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,MAAMuB,iBAAiB,GAAGA,CAAA,KAAM;IAC9B,IAAItD,OAAO,EAAE;MACX,oBACEX,OAAA;QAAKK,SAAS,EAAC,WAAW;QAAAiC,QAAA,EACvB,CAAC,GAAGO,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,KAAK,kBAC1BhD,OAAA;UAAiBK,SAAS,EAAC,eAAe;UAAAiC,QAAA,eACxCtC,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAiC,QAAA,gBAC7BtC,OAAA;cAAKK,SAAS,EAAC;YAAqC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC3D1C,OAAA;cAAKK,SAAS,EAAC;YAAoC;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC1D1C,OAAA;cAAKK,SAAS,EAAC;YAA8B;cAAAkC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC,GALEM,KAAK;UAAAT,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAMV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAEV;IAEA,IAAI7B,KAAK,IAAIN,WAAW,CAACG,WAAW,CAACQ,MAAM,KAAK,CAAC,EAAE;MACjD,OAAOiB,gBAAgB,CAACtB,KAAK,EAAEE,gBAAgB,CAAC;IAClD;IAEA,oBACEf,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAiC,QAAA,EACvB/B,WAAW,CAACG,WAAW,CAACuC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACH,GAAG,CAAC,CAACpB,IAAiB,EAAEsB,KAAK,kBAChEhD,OAAA,CAACJ,IAAI;QAEHsD,EAAE,EAAEjB,iBAAiB,CAACP,IAAI,CAAE;QAC5BrB,SAAS,EAAC,mLAAmL;QAAAiC,QAAA,eAE7LtC,OAAA;UAAKK,SAAS,EAAC,4BAA4B;UAAAiC,QAAA,gBAEzCtC,OAAA;YAAKK,SAAS,EAAC,eAAe;YAAAiC,QAAA,EAC3BZ,IAAI,CAACwC,SAAS,gBACblE,OAAA;cAAKK,SAAS,EAAC,UAAU;cAAAiC,QAAA,gBACvBtC,OAAA;gBACEmE,GAAG,EAAEzC,IAAI,CAACwC,SAAU;gBACpBE,GAAG,EAAE1C,IAAI,CAAC2C,KAAK,IAAI3C,IAAI,CAACE,IAAK;gBAC7BvB,SAAS,EAAC,0DAA0D;gBACpEiE,OAAO,EAAGC,CAAC,IAAK;kBACd,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;kBAC3CA,MAAM,CAACL,GAAG,GAAG,iCAAiC;gBAChD;cAAE;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACF1C,OAAA;gBAAKK,SAAS,EAAE,uEACd,CAACD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAAC+C,QAAQ,GAAG/C,IAAI,CAACgD,MAAM,IAC1D,cAAc,GACd,aAAa;cAChB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC,gBAEN1C,OAAA;cAAKK,SAAS,EAAC,0HAA0H;cAAAiC,QAAA,gBACvItC,OAAA;gBAAKK,SAAS,EAAC,uBAAuB;gBAACqD,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAtB,QAAA,eAC1FtC,OAAA;kBAAM6D,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAAoP;kBAAAzB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzT,CAAC,eACN1C,OAAA;gBAAKK,SAAS,EAAE,uEACd,CAACD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAAC+C,QAAQ,GAAG/C,IAAI,CAACgD,MAAM,IAC1D,cAAc,GACd,aAAa;cAChB;gBAAAnC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eACN1C,OAAA;YAAKK,SAAS,EAAC,gBAAgB;YAAAiC,QAAA,gBAC7BtC,OAAA;cAAIK,SAAS,EAAC,8GAA8G;cAAAiC,QAAA,EACzHZ,IAAI,CAAC2C;YAAK;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACT,CAAC,eACL1C,OAAA;cAAKK,SAAS,EAAC,wDAAwD;cAAAiC,QAAA,gBACrEtC,OAAA;gBAAMK,SAAS,EAAC,mBAAmB;gBAAAiC,QAAA,gBACjCtC,OAAA;kBAAKK,SAAS,EAAC,cAAc;kBAACqD,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAtB,QAAA,gBACjFtC,OAAA;oBAAM6D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAoF;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAC5J1C,OAAA;oBAAM6D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAkC;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvG,CAAC,EACLtC,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAACiD,YAAY,GAAGjD,IAAI,CAACkD,OAAO;cAAA;gBAAArC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC,eACP1C,OAAA;gBAAMK,SAAS,EAAC,eAAe;gBAAAiC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACxC1C,OAAA;gBAAMK,SAAS,EAAC,mBAAmB;gBAAAiC,QAAA,gBACjCtC,OAAA;kBAAKK,SAAS,EAAC,cAAc;kBAACqD,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAtB,QAAA,eACjFtC,OAAA;oBAAM6D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAoP;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzT,CAAC,EACLtC,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAACH,IAAI,GAAGG,IAAI,CAACmD,KAAK;cAAA;gBAAAtC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN1C,OAAA;cAAKK,SAAS,EAAC,mCAAmC;cAAAiC,QAAA,gBAChDtC,OAAA;gBAAKK,SAAS,EAAE,yEACd,CAACD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAAC+C,QAAQ,GAAG/C,IAAI,CAACgD,MAAM,IAC1D,qDAAqD,GACrD,kDAAkD,EACrD;gBAAApC,QAAA,gBACDtC,OAAA;kBAAMK,SAAS,EAAE,mCACf,CAACD,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAAC+C,QAAQ,GAAG/C,IAAI,CAACgD,MAAM,IAC1D,cAAc,GACd,aAAa;gBAChB;kBAAAnC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,EACV,CAACtC,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAGG,IAAI,CAAC+C,QAAQ,GAAG/C,IAAI,CAACgD,MAAM,IAC1DtE,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAG,QAAQ,GAAG,SAAS,GACtDnB,MAAM,CAACmB,IAAI,KAAK,eAAe,GAAG,UAAU,GAAG,QAAS;cAAA;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAE1D,CAAC,eACN1C,OAAA;gBAAKK,SAAS,EAAC,mEAAmE;gBAAAiC,QAAA,eAChFtC,OAAA;kBAAKK,SAAS,EAAC,uBAAuB;kBAACqD,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAtB,QAAA,eAC1FtC,OAAA;oBAAM6D,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAA8E;oBAAAzB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnJ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAhFDhB,IAAI,CAACQ,EAAE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAiFR,CACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV,CAAC;EAED,oBACE1C,OAAA,CAAAE,SAAA;IAAAoC,QAAA,eAEEtC,OAAA;MAAKK,SAAS,EAAE,sBAAsBA,SAAS,EAAG;MAAAiC,QAAA,GAE/CzB,KAAK,KAAKN,WAAW,CAACE,YAAY,CAACS,MAAM,GAAG,CAAC,IAAIX,WAAW,CAACG,WAAW,CAACQ,MAAM,GAAG,CAAC,CAAC,iBACnFlB,OAAA;QAAKK,SAAS,EAAC,gGAAgG;QAAAiC,QAAA,eAC7GtC,OAAA;UAAKK,SAAS,EAAC,mBAAmB;UAAAiC,QAAA,gBAChCtC,OAAA;YAAKK,SAAS,EAAC,eAAe;YAAAiC,QAAA,eAC5BtC,OAAA;cAAKK,SAAS,EAAC,yBAAyB;cAACqD,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAtB,QAAA,eAC5FtC,OAAA;gBAAM6D,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2I;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1C,OAAA;YAAKK,SAAS,EAAC,aAAa;YAAAiC,QAAA,eAC1BtC,OAAA;cAAMK,SAAS,EAAC,qCAAqC;cAAAiC,QAAA,EAAEzB;YAAK;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjE,CAAC,eACN1C,OAAA;YACE2C,OAAO,EAAE5B,gBAAiB;YAC1BV,SAAS,EAAC,wJAAwJ;YAAAiC,QAAA,EACnK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD1C,OAAA;QAAKK,SAAS,EAAC,uEAAuE;QAAAiC,QAAA,gBACpFtC,OAAA;UAAKK,SAAS,EAAC,+EAA+E;UAAAiC,QAAA,eAC5FtC,OAAA;YAAIK,SAAS,EAAC,uDAAuD;YAAAiC,QAAA,gBACnEtC,OAAA;cAAKK,SAAS,EAAC,sEAAsE;cAAAiC,QAAA,eACnFtC,OAAA;gBAAMK,SAAS,EAAC,eAAe;gBAAAiC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN1C,OAAA;cAAMK,SAAS,EAAC,QAAQ;cAAAiC,QAAA,EAAEhB,oBAAoB,CAAC;YAAC;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACvD,CAAC/B,OAAO,IAAIJ,WAAW,CAACE,YAAY,CAACS,MAAM,GAAG,CAAC,iBAC9ClB,OAAA;cAAMK,SAAS,EAAC,wEAAwE;cAAAiC,QAAA,EACrF/B,WAAW,CAACE,YAAY,CAACS;YAAM;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN1C,OAAA;UAAKK,SAAS,EAAC,KAAK;UAAAiC,QAAA,EACjBM,kBAAkB,CAAC;QAAC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1C,OAAA;QAAKK,SAAS,EAAC,uEAAuE;QAAAiC,QAAA,gBACpFtC,OAAA;UAAKK,SAAS,EAAC,iFAAiF;UAAAiC,QAAA,eAC9FtC,OAAA;YAAIK,SAAS,EAAC,uDAAuD;YAAAiC,QAAA,gBACnEtC,OAAA;cAAKK,SAAS,EAAC,uEAAuE;cAAAiC,QAAA,eACpFtC,OAAA;gBAAMK,SAAS,EAAC,gBAAgB;gBAAAiC,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACN1C,OAAA;cAAMK,SAAS,EAAC,QAAQ;cAAAiC,QAAA,EAAEd,mBAAmB,CAAC;YAAC;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EACtD,CAAC/B,OAAO,IAAIJ,WAAW,CAACG,WAAW,CAACQ,MAAM,GAAG,CAAC,iBAC7ClB,OAAA;cAAMK,SAAS,EAAC,0EAA0E;cAAAiC,QAAA,EACvF/B,WAAW,CAACG,WAAW,CAACQ;YAAM;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CACP;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACN1C,OAAA;UAAKK,SAAS,EAAC,KAAK;UAAAiC,QAAA,EACjB2B,iBAAiB,CAAC;QAAC;UAAA1B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjB,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC,gBACN,CAAC;AAEP,CAAC;AAACpC,EAAA,CA5WIH,mBAAuD;AAAA2E,EAAA,GAAvD3E,mBAAuD;AA8W7D,eAAeA,mBAAmB;AAAC,IAAA2E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}