{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UnifiedSidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst UnifiedSidebar = ({\n  config,\n  className = ''\n}) => {\n  _s();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [email, setEmail] = useState('');\n  const [subscribed, setSubscribed] = useState(false);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        limit: (config.limit * 2).toString(),\n        // Get more for different sections\n        exclude: config.currentItem\n      });\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      if (data.success) {\n        setScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar scholarships:', error);\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchScholarships();\n  }, [config.currentItem, config.limit]);\n  const getScholarshipUrl = scholarship => {\n    if (scholarship.slug) {\n      return `/bourse/${scholarship.slug}`;\n    }\n    return `/scholarships/${scholarship.id}`;\n  };\n  const handleNewsletterSubmit = async e => {\n    e.preventDefault();\n    if (!email) return;\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/newsletter`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          email\n        })\n      });\n      if (response.ok) {\n        setSubscribed(true);\n        setEmail('');\n        setTimeout(() => setSubscribed(false), 3000);\n      }\n    } catch (error) {\n      console.error('Newsletter subscription error:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `space-y-6 ${className}`,\n      children: [...Array(3)].map((_, i) => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl shadow-lg p-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"animate-pulse space-y-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"h-6 bg-gray-200 rounded w-3/4\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: [...Array(3)].map((_, j) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"h-12 w-16 bg-gray-200 rounded\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 100,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"h-4 bg-gray-200 rounded w-full\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 102,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 21\n              }, this)]\n            }, j, true, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 13\n        }, this)\n      }, i, false, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `space-y-6 ${className}`,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-gray-900 mb-2\",\n        children: \"Newsletter\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-sm text-gray-600 mb-4\",\n        children: \"Recevez les derni\\xE8res bourses directement dans votre bo\\xEEte mail.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleNewsletterSubmit,\n        className: \"flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"input\", {\n          type: \"email\",\n          value: email,\n          onChange: e => setEmail(e.target.value),\n          placeholder: \"Votre email\",\n          className: \"flex-1 px-4 py-2 text-sm border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\",\n          required: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          type: \"submit\",\n          className: `px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200 ${subscribed ? 'bg-green-600 text-white' : 'bg-blue-600 text-white hover:bg-blue-700'}`,\n          children: subscribed ? 'Abonné!' : 'S\\'abonner'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 117,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"Bourses Similaires\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 149,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-1 bg-blue-600 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: scholarships.length > 0 ? scholarships.slice(0, 5).map(scholarship => /*#__PURE__*/_jsxDEV(Link, {\n          to: getScholarshipUrl(scholarship),\n          className: \"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-shrink-0\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: scholarship.thumbnail || '/images/default-scholarship.jpg',\n              alt: scholarship.title,\n              className: \"w-16 h-12 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200\",\n              onError: e => {\n                const target = e.target;\n                target.src = '/images/default-scholarship.jpg';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 164,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 163,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 min-w-0\",\n            children: /*#__PURE__*/_jsxDEV(\"h4\", {\n              className: \"text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\",\n              children: scholarship.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 17\n          }, this)]\n        }, scholarship.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 158,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-400 mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-12 h-12 mx-auto\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm text-gray-500\",\n            children: \"Aucune bourse disponible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 189,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 155,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mb-6\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-lg font-semibold text-gray-900 mb-2\",\n          children: \"Derni\\xE8res Bourses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 198,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-1 bg-green-600 rounded\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 201,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 197,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-2 gap-3\",\n        children: scholarships.length > 0 ? scholarships.slice(5, 11).map(scholarship => /*#__PURE__*/_jsxDEV(Link, {\n          to: getScholarshipUrl(scholarship),\n          className: \"block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: scholarship.thumbnail || '/images/default-scholarship.jpg',\n              alt: scholarship.title,\n              className: \"w-full h-20 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200\",\n              onError: e => {\n                const target = e.target;\n                target.src = '/images/default-scholarship.jpg';\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 213,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 212,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-xs font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\",\n            children: scholarship.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 17\n          }, this)]\n        }, scholarship.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 15\n        }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"col-span-2 text-center py-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-gray-400 mb-2\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-8 h-8 mx-auto\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 233,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 232,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 231,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-xs text-gray-500\",\n            children: \"Aucune bourse disponible\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 230,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 196,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n_s(UnifiedSidebar, \"lnGawvreI5sL8LwcLNA+lSoKE0w=\");\n_c = UnifiedSidebar;\nexport default UnifiedSidebar;\nvar _c;\n$RefreshReg$(_c, \"UnifiedSidebar\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Link", "jsxDEV", "_jsxDEV", "UnifiedSidebar", "config", "className", "_s", "scholarships", "setScholarships", "loading", "setLoading", "email", "setEmail", "subscribed", "setSubscribed", "fetchScholarships", "params", "URLSearchParams", "limit", "toString", "exclude", "currentItem", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "Error", "data", "json", "success", "error", "console", "getScholarshipUrl", "scholarship", "slug", "id", "handleNewsletterSubmit", "e", "preventDefault", "method", "headers", "body", "JSON", "stringify", "setTimeout", "children", "Array", "map", "_", "i", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "j", "onSubmit", "type", "value", "onChange", "target", "placeholder", "required", "length", "slice", "to", "src", "thumbnail", "alt", "title", "onError", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/UnifiedSidebar.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Link } from 'react-router-dom';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  thumbnail: string;\n  slug?: string;\n}\n\ninterface UnifiedSidebarProps {\n  config: {\n    type: 'levels' | 'countries' | 'opportunities';\n    currentItem: string;\n    limit: number;\n  };\n  className?: string;\n}\n\nconst UnifiedSidebar: React.FC<UnifiedSidebarProps> = ({ config, className = '' }) => {\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [email, setEmail] = useState('');\n  const [subscribed, setSubscribed] = useState(false);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      \n      const params = new URLSearchParams({\n        limit: (config.limit * 2).toString(), // Get more for different sections\n        exclude: config.currentItem\n      });\n\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/scholarships/latest?${params}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setScholarships(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching sidebar scholarships:', error);\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchScholarships();\n  }, [config.currentItem, config.limit]);\n\n  const getScholarshipUrl = (scholarship: Scholarship) => {\n    if (scholarship.slug) {\n      return `/bourse/${scholarship.slug}`;\n    }\n    return `/scholarships/${scholarship.id}`;\n  };\n\n  const handleNewsletterSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!email) return;\n\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';\n      const response = await fetch(`${apiUrl}/api/newsletter`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email }),\n      });\n\n      if (response.ok) {\n        setSubscribed(true);\n        setEmail('');\n        setTimeout(() => setSubscribed(false), 3000);\n      }\n    } catch (error) {\n      console.error('Newsletter subscription error:', error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className={`space-y-6 ${className}`}>\n        {[...Array(3)].map((_, i) => (\n          <div key={i} className=\"bg-white rounded-xl shadow-lg p-6\">\n            <div className=\"animate-pulse space-y-4\">\n              <div className=\"h-6 bg-gray-200 rounded w-3/4\"></div>\n              <div className=\"space-y-3\">\n                {[...Array(3)].map((_, j) => (\n                  <div key={j} className=\"flex items-center space-x-3\">\n                    <div className=\"h-12 w-16 bg-gray-200 rounded\"></div>\n                    <div className=\"flex-1\">\n                      <div className=\"h-4 bg-gray-200 rounded w-full\"></div>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className={`space-y-6 ${className}`}>\n      {/* Newsletter Subscription - Top Position */}\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <h4 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          Newsletter\n        </h4>\n        <p className=\"text-sm text-gray-600 mb-4\">\n          Recevez les dernières bourses directement dans votre boîte mail.\n        </p>\n        <form onSubmit={handleNewsletterSubmit} className=\"flex\">\n          <input\n            type=\"email\"\n            value={email}\n            onChange={(e) => setEmail(e.target.value)}\n            placeholder=\"Votre email\"\n            className=\"flex-1 px-4 py-2 text-sm border border-gray-300 rounded-l-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            required\n          />\n          <button \n            type=\"submit\"\n            className={`px-4 py-2 text-sm font-medium rounded-r-lg transition-colors duration-200 ${\n              subscribed \n                ? 'bg-green-600 text-white' \n                : 'bg-blue-600 text-white hover:bg-blue-700'\n            }`}\n          >\n            {subscribed ? 'Abonné!' : 'S\\'abonner'}\n          </button>\n        </form>\n      </div>\n\n      {/* Similar Content - Middle Position */}\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n            Bourses Similaires\n          </h3>\n          <div className=\"w-12 h-1 bg-blue-600 rounded\"></div>\n        </div>\n\n        <div className=\"space-y-4\">\n          {scholarships.length > 0 ? (\n            scholarships.slice(0, 5).map((scholarship) => (\n              <Link\n                key={scholarship.id}\n                to={getScholarshipUrl(scholarship)}\n                className=\"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\"\n              >\n                <div className=\"flex-shrink-0\">\n                  <img\n                    src={scholarship.thumbnail || '/images/default-scholarship.jpg'}\n                    alt={scholarship.title}\n                    className=\"w-16 h-12 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200\"\n                    onError={(e) => {\n                      const target = e.target as HTMLImageElement;\n                      target.src = '/images/default-scholarship.jpg';\n                    }}\n                  />\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                    {scholarship.title}\n                  </h4>\n                </div>\n              </Link>\n            ))\n          ) : (\n            <div className=\"text-center py-8\">\n              <div className=\"text-gray-400 mb-2\">\n                <svg className=\"w-12 h-12 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <p className=\"text-sm text-gray-500\">Aucune bourse disponible</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Latest Scholarships - Bottom Position in 3x2 Card Layout */}\n      <div className=\"bg-white rounded-xl shadow-lg p-6\">\n        <div className=\"mb-6\">\n          <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n            Dernières Bourses\n          </h3>\n          <div className=\"w-12 h-1 bg-green-600 rounded\"></div>\n        </div>\n\n        <div className=\"grid grid-cols-2 gap-3\">\n          {scholarships.length > 0 ? (\n            scholarships.slice(5, 11).map((scholarship) => (\n              <Link\n                key={scholarship.id}\n                to={getScholarshipUrl(scholarship)}\n                className=\"block p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200 group\"\n              >\n                <div className=\"mb-2\">\n                  <img\n                    src={scholarship.thumbnail || '/images/default-scholarship.jpg'}\n                    alt={scholarship.title}\n                    className=\"w-full h-20 object-cover rounded-md shadow-sm group-hover:shadow-md transition-shadow duration-200\"\n                    onError={(e) => {\n                      const target = e.target as HTMLImageElement;\n                      target.src = '/images/default-scholarship.jpg';\n                    }}\n                  />\n                </div>\n\n                <h4 className=\"text-xs font-medium text-gray-900 group-hover:text-blue-600 transition-colors duration-200 line-clamp-2\">\n                  {scholarship.title}\n                </h4>\n              </Link>\n            ))\n          ) : (\n            <div className=\"col-span-2 text-center py-8\">\n              <div className=\"text-gray-400 mb-2\">\n                <svg className=\"w-8 h-8 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                </svg>\n              </div>\n              <p className=\"text-xs text-gray-500\">Aucune bourse disponible</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default UnifiedSidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAkBxC,MAAMC,cAA6C,GAAGA,CAAC;EAAEC,MAAM;EAAEC,SAAS,GAAG;AAAG,CAAC,KAAK;EAAAC,EAAA;EACpF,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGV,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACW,OAAO,EAAEC,UAAU,CAAC,GAAGZ,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACa,KAAK,EAAEC,QAAQ,CAAC,GAAGd,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,KAAK,CAAC;EAEnD,MAAMiB,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFL,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMM,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,KAAK,EAAE,CAACd,MAAM,CAACc,KAAK,GAAG,CAAC,EAAEC,QAAQ,CAAC,CAAC;QAAE;QACtCC,OAAO,EAAEhB,MAAM,CAACiB;MAClB,CAAC,CAAC;MAEF,MAAMC,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,4BAA4BN,MAAM,EAAE,CAAC;MAE3E,IAAI,CAACU,QAAQ,CAACE,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMJ,QAAQ,CAACK,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBxB,eAAe,CAACsB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,EAAEA,KAAK,CAAC;MAC5DzB,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDX,SAAS,CAAC,MAAM;IACdgB,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACX,MAAM,CAACiB,WAAW,EAAEjB,MAAM,CAACc,KAAK,CAAC,CAAC;EAEtC,MAAMiB,iBAAiB,GAAIC,WAAwB,IAAK;IACtD,IAAIA,WAAW,CAACC,IAAI,EAAE;MACpB,OAAO,WAAWD,WAAW,CAACC,IAAI,EAAE;IACtC;IACA,OAAO,iBAAiBD,WAAW,CAACE,EAAE,EAAE;EAC1C,CAAC;EAED,MAAMC,sBAAsB,GAAG,MAAOC,CAAkB,IAAK;IAC3DA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAAC9B,KAAK,EAAE;IAEZ,IAAI;MACF,MAAMW,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,iBAAiB,EAAE;QACvDoB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UAAEnC;QAAM,CAAC;MAChC,CAAC,CAAC;MAEF,IAAIe,QAAQ,CAACE,EAAE,EAAE;QACfd,aAAa,CAAC,IAAI,CAAC;QACnBF,QAAQ,CAAC,EAAE,CAAC;QACZmC,UAAU,CAAC,MAAMjC,aAAa,CAAC,KAAK,CAAC,EAAE,IAAI,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOmB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACxD;EACF,CAAC;EAED,IAAIxB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKG,SAAS,EAAE,aAAaA,SAAS,EAAG;MAAA2C,QAAA,EACtC,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEC,CAAC,kBACtBlD,OAAA;QAAaG,SAAS,EAAC,mCAAmC;QAAA2C,QAAA,eACxD9C,OAAA;UAAKG,SAAS,EAAC,yBAAyB;UAAA2C,QAAA,gBACtC9C,OAAA;YAAKG,SAAS,EAAC;UAA+B;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACrDtD,OAAA;YAAKG,SAAS,EAAC,WAAW;YAAA2C,QAAA,EACvB,CAAC,GAAGC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,GAAG,CAAC,CAACC,CAAC,EAAEM,CAAC,kBACtBvD,OAAA;cAAaG,SAAS,EAAC,6BAA6B;cAAA2C,QAAA,gBAClD9C,OAAA;gBAAKG,SAAS,EAAC;cAA+B;gBAAAgD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eACrDtD,OAAA;gBAAKG,SAAS,EAAC,QAAQ;gBAAA2C,QAAA,eACrB9C,OAAA;kBAAKG,SAAS,EAAC;gBAAgC;kBAAAgD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnD,CAAC;YAAA,GAJEC,CAAC;cAAAJ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKN,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GAbEJ,CAAC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAcN,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEtD,OAAA;IAAKG,SAAS,EAAE,aAAaA,SAAS,EAAG;IAAA2C,QAAA,gBAEvC9C,OAAA;MAAKG,SAAS,EAAC,mCAAmC;MAAA2C,QAAA,gBAChD9C,OAAA;QAAIG,SAAS,EAAC,0CAA0C;QAAA2C,QAAA,EAAC;MAEzD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACLtD,OAAA;QAAGG,SAAS,EAAC,4BAA4B;QAAA2C,QAAA,EAAC;MAE1C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJtD,OAAA;QAAMwD,QAAQ,EAAEnB,sBAAuB;QAAClC,SAAS,EAAC,MAAM;QAAA2C,QAAA,gBACtD9C,OAAA;UACEyD,IAAI,EAAC,OAAO;UACZC,KAAK,EAAEjD,KAAM;UACbkD,QAAQ,EAAGrB,CAAC,IAAK5B,QAAQ,CAAC4B,CAAC,CAACsB,MAAM,CAACF,KAAK,CAAE;UAC1CG,WAAW,EAAC,aAAa;UACzB1D,SAAS,EAAC,2IAA2I;UACrJ2D,QAAQ;QAAA;UAAAX,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,eACFtD,OAAA;UACEyD,IAAI,EAAC,QAAQ;UACbtD,SAAS,EAAE,6EACTQ,UAAU,GACN,yBAAyB,GACzB,0CAA0C,EAC7C;UAAAmC,QAAA,EAEFnC,UAAU,GAAG,SAAS,GAAG;QAAY;UAAAwC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,eAGNtD,OAAA;MAAKG,SAAS,EAAC,mCAAmC;MAAA2C,QAAA,gBAChD9C,OAAA;QAAKG,SAAS,EAAC,MAAM;QAAA2C,QAAA,gBACnB9C,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAA2C,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAKG,SAAS,EAAC;QAA8B;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjD,CAAC,eAENtD,OAAA;QAAKG,SAAS,EAAC,WAAW;QAAA2C,QAAA,EACvBzC,YAAY,CAAC0D,MAAM,GAAG,CAAC,GACtB1D,YAAY,CAAC2D,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAChB,GAAG,CAAEd,WAAW,iBACvClC,OAAA,CAACF,IAAI;UAEHmE,EAAE,EAAEhC,iBAAiB,CAACC,WAAW,CAAE;UACnC/B,SAAS,EAAC,kGAAkG;UAAA2C,QAAA,gBAE5G9C,OAAA;YAAKG,SAAS,EAAC,eAAe;YAAA2C,QAAA,eAC5B9C,OAAA;cACEkE,GAAG,EAAEhC,WAAW,CAACiC,SAAS,IAAI,iCAAkC;cAChEC,GAAG,EAAElC,WAAW,CAACmC,KAAM;cACvBlE,SAAS,EAAC,kGAAkG;cAC5GmE,OAAO,EAAGhC,CAAC,IAAK;gBACd,MAAMsB,MAAM,GAAGtB,CAAC,CAACsB,MAA0B;gBAC3CA,MAAM,CAACM,GAAG,GAAG,iCAAiC;cAChD;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAKG,SAAS,EAAC,gBAAgB;YAAA2C,QAAA,eAC7B9C,OAAA;cAAIG,SAAS,EAAC,yGAAyG;cAAA2C,QAAA,EACpHZ,WAAW,CAACmC;YAAK;cAAAlB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA,GApBDpB,WAAW,CAACE,EAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqBf,CACP,CAAC,gBAEFtD,OAAA;UAAKG,SAAS,EAAC,kBAAkB;UAAA2C,QAAA,gBAC/B9C,OAAA;YAAKG,SAAS,EAAC,oBAAoB;YAAA2C,QAAA,eACjC9C,OAAA;cAAKG,SAAS,EAAC,mBAAmB;cAACoE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACtF9C,OAAA;gBAAM0E,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsH;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3L;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtD,OAAA;YAAGG,SAAS,EAAC,uBAAuB;YAAA2C,QAAA,EAAC;UAAwB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtD,OAAA;MAAKG,SAAS,EAAC,mCAAmC;MAAA2C,QAAA,gBAChD9C,OAAA;QAAKG,SAAS,EAAC,MAAM;QAAA2C,QAAA,gBACnB9C,OAAA;UAAIG,SAAS,EAAC,0CAA0C;UAAA2C,QAAA,EAAC;QAEzD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACLtD,OAAA;UAAKG,SAAS,EAAC;QAA+B;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC,eAENtD,OAAA;QAAKG,SAAS,EAAC,wBAAwB;QAAA2C,QAAA,EACpCzC,YAAY,CAAC0D,MAAM,GAAG,CAAC,GACtB1D,YAAY,CAAC2D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAChB,GAAG,CAAEd,WAAW,iBACxClC,OAAA,CAACF,IAAI;UAEHmE,EAAE,EAAEhC,iBAAiB,CAACC,WAAW,CAAE;UACnC/B,SAAS,EAAC,4EAA4E;UAAA2C,QAAA,gBAEtF9C,OAAA;YAAKG,SAAS,EAAC,MAAM;YAAA2C,QAAA,eACnB9C,OAAA;cACEkE,GAAG,EAAEhC,WAAW,CAACiC,SAAS,IAAI,iCAAkC;cAChEC,GAAG,EAAElC,WAAW,CAACmC,KAAM;cACvBlE,SAAS,EAAC,oGAAoG;cAC9GmE,OAAO,EAAGhC,CAAC,IAAK;gBACd,MAAMsB,MAAM,GAAGtB,CAAC,CAACsB,MAA0B;gBAC3CA,MAAM,CAACM,GAAG,GAAG,iCAAiC;cAChD;YAAE;cAAAf,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENtD,OAAA;YAAIG,SAAS,EAAC,yGAAyG;YAAA2C,QAAA,EACpHZ,WAAW,CAACmC;UAAK;YAAAlB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC;QAAA,GAlBApB,WAAW,CAACE,EAAE;UAAAe,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBf,CACP,CAAC,gBAEFtD,OAAA;UAAKG,SAAS,EAAC,6BAA6B;UAAA2C,QAAA,gBAC1C9C,OAAA;YAAKG,SAAS,EAAC,oBAAoB;YAAA2C,QAAA,eACjC9C,OAAA;cAAKG,SAAS,EAAC,iBAAiB;cAACoE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAA3B,QAAA,eACpF9C,OAAA;gBAAM0E,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAsH;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3L;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtD,OAAA;YAAGG,SAAS,EAAC,uBAAuB;YAAA2C,QAAA,EAAC;UAAwB;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAClD,EAAA,CA/NIH,cAA6C;AAAA6E,EAAA,GAA7C7E,cAA6C;AAiOnD,eAAeA,cAAc;AAAC,IAAA6E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}