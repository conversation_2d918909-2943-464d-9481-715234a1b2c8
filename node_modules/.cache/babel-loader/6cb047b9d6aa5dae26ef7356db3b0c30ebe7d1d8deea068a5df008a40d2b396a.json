{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeatureHighlightsSection = () => {\n  const features = [{\n    id: 'search',\n    title: 'Recherche Avancée',\n    description: 'Trouvez rapidement des bourses adaptées à votre profil grâce à nos filtres avancés',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 9\n    }, this),\n    color: 'blue'\n  }, {\n    id: 'notifications',\n    title: 'Alertes Personnalisées',\n    description: 'Recevez des notifications pour les bourses correspondant à vos critères',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 21,\n      columnNumber: 9\n    }, this),\n    color: 'purple'\n  }, {\n    id: 'application',\n    title: 'Suivi de Candidature',\n    description: 'Gérez et suivez l\\'état de vos candidatures de bourse en un seul endroit',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this),\n    color: 'green'\n  }, {\n    id: 'resources',\n    title: 'Ressources et Conseils',\n    description: 'Accédez à des guides et conseils pour maximiser vos chances d\\'obtenir une bourse',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      viewBox: \"0 0 24 24\",\n      stroke: \"currentColor\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 43,\n      columnNumber: 9\n    }, this),\n    color: 'indigo'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"py-8 bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-2\",\n          children: \"Pourquoi Choisir MaBourse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: \"Une Plateforme Compl\\xE8te pour Votre Recherche de Bourses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 59,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: features.map(feature => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative bg-white rounded-2xl shadow-lg border border-gray-100 p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: `flex items-center justify-center w-14 h-14 rounded-xl bg-${feature.color}-100 text-${feature.color}-600 mb-6`,\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-xl font-bold text-gray-900 mb-3\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 80,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `absolute top-0 right-0 w-24 h-24 bg-${feature.color}-500/5 rounded-bl-full -z-10`\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 85,\n            columnNumber: 15\n          }, this)]\n        }, feature.id, true, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 53,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 5\n  }, this);\n};\n_c = FeatureHighlightsSection;\nexport default FeatureHighlightsSection;\nvar _c;\n$RefreshReg$(_c, \"FeatureHighlightsSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FeatureHighlightsSection", "features", "id", "title", "description", "icon", "className", "fill", "viewBox", "stroke", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "map", "feature", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/FeatureHighlightsSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst FeatureHighlightsSection: React.FC = () => {\n  const features = [\n    {\n      id: 'search',\n      title: 'Recherche Avancée',\n      description: 'Trouvez rapidement des bourses adaptées à votre profil grâce à nos filtres avancés',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n        </svg>\n      ),\n      color: 'blue'\n    },\n    {\n      id: 'notifications',\n      title: 'Alertes Personnalisées',\n      description: 'Recevez des notifications pour les bourses correspondant à vos critères',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9\" />\n        </svg>\n      ),\n      color: 'purple'\n    },\n    {\n      id: 'application',\n      title: 'Suivi de Candidature',\n      description: 'Gérez et suivez l\\'état de vos candidatures de bourse en un seul endroit',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01\" />\n        </svg>\n      ),\n      color: 'green'\n    },\n    {\n      id: 'resources',\n      title: 'Ressources et Conseils',\n      description: 'Accédez à des guides et conseils pour maximiser vos chances d\\'obtenir une bourse',\n      icon: (\n        <svg className=\"w-6 h-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\" />\n        </svg>\n      ),\n      color: 'indigo'\n    }\n  ];\n\n  return (\n    <section className=\"py-8 bg-white\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section header */}\n        <div className=\"text-center mb-8\">\n          <span className=\"inline-block px-3 py-1 rounded-full bg-primary/10 text-primary text-sm font-medium mb-2\">\n            Pourquoi Choisir MaBourse\n          </span>\n          <h2 className=\"text-2xl font-bold text-gray-900\">\n            Une Plateforme Complète pour Votre Recherche de Bourses\n          </h2>\n        </div>\n\n        {/* Features grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature) => (\n            <div\n              key={feature.id}\n              className=\"relative bg-white rounded-2xl shadow-lg border border-gray-100 p-6 transition-all duration-300 hover:shadow-xl hover:-translate-y-1\"\n            >\n              {/* Icon */}\n              <div className={`flex items-center justify-center w-14 h-14 rounded-xl bg-${feature.color}-100 text-${feature.color}-600 mb-6`}>\n                {feature.icon}\n              </div>\n\n              {/* Content */}\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                {feature.title}\n              </h3>\n              <p className=\"text-gray-600\">\n                {feature.description}\n              </p>\n\n              {/* Decorative element */}\n              <div className={`absolute top-0 right-0 w-24 h-24 bg-${feature.color}-500/5 rounded-bl-full -z-10`}></div>\n            </div>\n          ))}\n        </div>\n\n\n      </div>\n    </section>\n  );\n};\n\nexport default FeatureHighlightsSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,wBAAkC,GAAGA,CAAA,KAAM;EAC/C,MAAMC,QAAQ,GAAG,CACf;IACEC,EAAE,EAAE,QAAQ;IACZC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,oFAAoF;IACjGC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA6C;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClH,CACN;IACDC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,EAAE,EAAE,eAAe;IACnBC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,yEAAyE;IACtFC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA+L;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpQ,CACN;IACDC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,EAAE,EAAE,aAAa;IACjBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,0EAA0E;IACvFC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAiK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtO,CACN;IACDC,KAAK,EAAE;EACT,CAAC,EACD;IACEjB,EAAE,EAAE,WAAW;IACfC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,mFAAmF;IAChGC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,OAAO,EAAC,WAAW;MAACC,MAAM,EAAC,cAAc;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAoP;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzT,CACN;IACDC,KAAK,EAAE;EACT,CAAC,CACF;EAED,oBACEpB,OAAA;IAASO,SAAS,EAAC,eAAe;IAAAI,QAAA,eAChCX,OAAA;MAAKO,SAAS,EAAC,wCAAwC;MAAAI,QAAA,gBAErDX,OAAA;QAAKO,SAAS,EAAC,kBAAkB;QAAAI,QAAA,gBAC/BX,OAAA;UAAMO,SAAS,EAAC,yFAAyF;UAAAI,QAAA,EAAC;QAE1G;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACPnB,OAAA;UAAIO,SAAS,EAAC,kCAAkC;UAAAI,QAAA,EAAC;QAEjD;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGNnB,OAAA;QAAKO,SAAS,EAAC,sDAAsD;QAAAI,QAAA,EAClET,QAAQ,CAACmB,GAAG,CAAEC,OAAO,iBACpBtB,OAAA;UAEEO,SAAS,EAAC,qIAAqI;UAAAI,QAAA,gBAG/IX,OAAA;YAAKO,SAAS,EAAE,4DAA4De,OAAO,CAACF,KAAK,aAAaE,OAAO,CAACF,KAAK,WAAY;YAAAT,QAAA,EAC5HW,OAAO,CAAChB;UAAI;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAGNnB,OAAA;YAAIO,SAAS,EAAC,sCAAsC;YAAAI,QAAA,EACjDW,OAAO,CAAClB;UAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACZ,CAAC,eACLnB,OAAA;YAAGO,SAAS,EAAC,eAAe;YAAAI,QAAA,EACzBW,OAAO,CAACjB;UAAW;YAAAW,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnB,CAAC,eAGJnB,OAAA;YAAKO,SAAS,EAAE,uCAAuCe,OAAO,CAACF,KAAK;UAA+B;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA,GAjBrGG,OAAO,CAACnB,EAAE;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkBZ,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACI,EAAA,GA3FItB,wBAAkC;AA6FxC,eAAeA,wBAAwB;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}