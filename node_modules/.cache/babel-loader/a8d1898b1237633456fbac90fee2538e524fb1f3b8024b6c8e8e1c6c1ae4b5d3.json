{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\nimport StandardizedFilters from '../components/filters/StandardizedFilters';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Scholarships = () => {\n  _s();\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 9,\n    // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white min-h-screen\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6 animate-fade-in\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 140,\n              columnNumber: 15\n            }, this), pagination.total, \" bourses disponibles\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 139,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\",\n            children: [\"Trouvez Votre\", /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"block text-yellow-300\",\n              children: \"Bourse Id\\xE9ale\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 146,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 133,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-gray-100 py-6 md:py-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"article\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-2\",\n              children: \"Toutes les Bourses d'\\xC9tudes Disponibles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600 text-base\",\n              children: \"Explorez notre collection compl\\xE8te d'opportunit\\xE9s de financement pour tous les niveaux d'\\xE9tudes\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"prose max-w-none\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\",\n              children: \"Bienvenue sur la plateforme de r\\xE9f\\xE9rence pour les bourses d'\\xE9tudes internationales. Cette page centralise toutes les opportunit\\xE9s de financement disponibles, soigneusement v\\xE9rifi\\xE9es et mises \\xE0 jour quotidiennement pour vous offrir les meilleures chances de r\\xE9ussite dans vos projets acad\\xE9miques.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\",\n              children: \"Vous trouverez ici des bourses pour tous les niveaux d'\\xE9tudes - de la licence au doctorat - propos\\xE9es par des gouvernements, des universit\\xE9s prestigieuses, des fondations internationales et des organisations priv\\xE9es. Chaque opportunit\\xE9 est accompagn\\xE9e d'informations d\\xE9taill\\xE9es sur les crit\\xE8res d'\\xE9ligibilit\\xE9, les avantages financiers, les proc\\xE9dures de candidature et les \\xE9ch\\xE9ances importantes.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 leading-relaxed text-sm md:text-base text-justify\",\n              children: \"Utilisez les filtres avanc\\xE9s ci-dessous pour affiner votre recherche selon votre niveau d'\\xE9tudes, votre pays de destination pr\\xE9f\\xE9r\\xE9, votre domaine d'\\xE9tudes ou le type de financement souhait\\xE9. Notre syst\\xE8me de recherche intelligent vous permettra de d\\xE9couvrir rapidement les bourses qui correspondent parfaitement \\xE0 votre profil et \\xE0 vos ambitions acad\\xE9miques.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 178,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 156,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 155,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block\",\n      children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n        adSlot: \"1234567890\",\n        adSize: \"leaderboard\",\n        responsive: true,\n        fullWidth: true\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\",\n      children: /*#__PURE__*/_jsxDEV(StandardizedFilters, {\n        searchQuery: searchQuery,\n        selectedLevel: selectedLevel,\n        selectedCountry: selectedCountry,\n        selectedStatus: selectedStatus,\n        onSearchChange: query => {\n          setSearchQuery(query);\n          setPagination(prev => ({\n            ...prev,\n            page: 1\n          }));\n        },\n        onLevelChange: level => {\n          setSelectedLevel(level);\n          setPagination(prev => ({\n            ...prev,\n            page: 1\n          }));\n        },\n        onCountryChange: country => {\n          setSelectedCountry(country);\n          setPagination(prev => ({\n            ...prev,\n            page: 1\n          }));\n        },\n        onStatusChange: status => {\n          setSelectedStatus(status);\n          setPagination(prev => ({\n            ...prev,\n            page: 1\n          }));\n        },\n        onReset: () => {\n          setSearchQuery('');\n          setSelectedLevel('');\n          setSelectedCountry('');\n          setSelectedStatus('');\n          setPagination(prev => ({\n            ...prev,\n            page: 1\n          }));\n        },\n        className: \"mb-12 transform translate-y-0 animate-slide-up\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 198,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 197,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n              className: \"text-2xl font-bold text-gray-900 mb-2\",\n              children: \"R\\xE9sultats de recherche\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 237,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-600\",\n              children: !loading && !error && `${pagination.total} bourses trouvées`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 238,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 236,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4 md:mt-0\",\n            children: /*#__PURE__*/_jsxDEV(\"select\", {\n              className: \"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\",\n              children: [/*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"recent\",\n                children: \"Plus r\\xE9centes\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 246,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"deadline\",\n                children: \"Date limite proche\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 247,\n                columnNumber: 15\n              }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n                value: \"relevance\",\n                children: \"Pertinence\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 15\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 243,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 242,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 235,\n          columnNumber: 9\n        }, this), loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center items-center py-16\",\n          children: /*#__PURE__*/_jsxDEV(Spin, {\n            size: \"large\",\n            tip: \"Chargement des bourses...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n          message: \"Erreur\",\n          description: error,\n          type: \"error\",\n          showIcon: true,\n          className: \"mb-6 rounded-xl shadow-md\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mb-8 md:hidden\",\n            children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n              adSlot: \"2345678901\",\n              adSize: \"rectangle\",\n              responsive: true,\n              fullWidth: true\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 269,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 268,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\",\n            children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"animate-fade-in\",\n              style: {\n                animationDelay: `${index * 0.1}s`\n              },\n              children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                id: scholarship.id,\n                title: scholarship.title,\n                thumbnail: scholarship.thumbnail,\n                deadline: scholarship.deadline,\n                isOpen: scholarship.isOpen,\n                level: scholarship.level,\n                country: scholarship.country,\n                onClick: (id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`,\n                index: index\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 280,\n                columnNumber: 19\n              }, this)\n            }, scholarship.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 279,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this), scholarships.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"mx-auto h-12 w-12 text-gray-400\",\n              fill: \"none\",\n              viewBox: \"0 0 24 24\",\n              stroke: \"currentColor\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 1.5,\n                d: \"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 299,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 298,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"mt-4 text-lg font-medium text-gray-900\",\n              children: \"Aucune bourse trouv\\xE9e\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 301,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"mt-2 text-sm text-gray-500 max-w-md mx-auto\",\n              children: \"Essayez d'ajuster vos crit\\xE8res de recherche ou de filtrage pour trouver ce que vous cherchez.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 302,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              onClick: () => {\n                setSearchQuery('');\n                setSelectedLevel('');\n                setSelectedCountry('');\n                setPagination(prev => ({\n                  ...prev,\n                  page: 1\n                }));\n              },\n              className: \"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\",\n              children: \"R\\xE9initialiser les filtres\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 297,\n            columnNumber: 15\n          }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex justify-center mt-12\",\n            children: /*#__PURE__*/_jsxDEV(Pagination, {\n              current: pagination.page,\n              total: pagination.total,\n              pageSize: pagination.limit,\n              onChange: handlePageChange,\n              showSizeChanger: false,\n              showQuickJumper: true,\n              showTotal: total => `Total ${total} bourses`,\n              className: \"shadow-sm rounded-xl p-2 bg-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 231,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(PageEndSuggestions, {\n      currentPageType: \"scholarship\",\n      currentItem: selectedLevel || selectedCountry\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 131,\n    columnNumber: 5\n  }, this);\n};\n_s(Scholarships, \"3AnW/tSCM17S80NO8LJJzx7Q0dc=\", false, function () {\n  return [useLocation];\n});\n_c = Scholarships;\nexport default Scholarships;\nvar _c;\n$RefreshReg$(_c, \"Scholarships\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Pagination", "Spin", "<PERSON><PERSON>", "useLocation", "EnhancedScholarshipCard", "PageEndSuggestions", "AdPlacement", "StandardizedFilters", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Scholarships", "_s", "location", "searchQuery", "setSearch<PERSON>uery", "selectedLevel", "setSelectedLevel", "selectedCountry", "setSelectedCountry", "selectedStatus", "setSelectedStatus", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "searchParams", "URLSearchParams", "search", "levelParam", "get", "countryParam", "fetchScholarships", "params", "append", "toString", "response", "fetch", "process", "env", "REACT_APP_API_URL", "ok", "Error", "data", "json", "console", "log", "scholarshipsData", "paginationData", "length", "Math", "ceil", "err", "handlePageChange", "prev", "window", "scrollTo", "top", "behavior", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "adSlot", "adSize", "responsive", "fullWidth", "onSearchChange", "query", "onLevelChange", "level", "onCountryChange", "country", "onStatusChange", "status", "onReset", "value", "size", "tip", "message", "description", "type", "showIcon", "map", "scholarship", "index", "style", "animationDelay", "id", "title", "thumbnail", "deadline", "isOpen", "onClick", "slug", "href", "fill", "viewBox", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "current", "pageSize", "onChange", "showSizeChanger", "showQuickJumper", "showTotal", "currentPageType", "currentItem", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/pages/Scholarships.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { useLocation } from 'react-router-dom';\nimport EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';\nimport SimplifiedSidebar from '../components/SimplifiedSidebar';\nimport PageEndSuggestions from '../components/PageEndSuggestions';\nimport AdPlacement from '../components/AdPlacement';\nimport StandardizedFilters from '../components/filters/StandardizedFilters';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\nconst Scholarships: React.FC = () => {\n  const location = useLocation();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedLevel, setSelectedLevel] = useState('');\n  const [selectedCountry, setSelectedCountry] = useState('');\n  const [selectedStatus, setSelectedStatus] = useState('');\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 9, // Show 9 scholarships per page (3x3 grid)\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Read URL parameters on component mount\n  useEffect(() => {\n    const searchParams = new URLSearchParams(location.search);\n    const levelParam = searchParams.get('level');\n    const countryParam = searchParams.get('country');\n\n    if (levelParam) {\n      setSelectedLevel(levelParam);\n    }\n    if (countryParam) {\n      setSelectedCountry(countryParam);\n    }\n  }, [location.search]);\n\n  // Fetch scholarships with pagination and filters\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n\n      // Build query parameters\n      const params = new URLSearchParams();\n      params.append('page', pagination.page.toString());\n      params.append('limit', pagination.limit.toString());\n\n      if (searchQuery) {\n        params.append('q', searchQuery);\n      }\n\n      if (selectedLevel) {\n        params.append('level', selectedLevel);\n      }\n\n      if (selectedCountry) {\n        params.append('country', selectedCountry);\n      }\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5001'}/api/scholarships/search?${params.toString()}`);\n\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      console.log('Scholarships search API response:', data);\n\n      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }\n      const scholarshipsData = data.data || data.scholarships || [];\n      const paginationData = data.pagination || {};\n\n      setScholarships(scholarshipsData);\n      setPagination(paginationData || {\n        total: scholarshipsData.length || 0,\n        page: 1,\n        limit: 9,\n        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),\n        hasNextPage: false,\n        hasPreviousPage: false\n      });\n      setError(null);\n    } catch (err) {\n      console.error('Error fetching scholarships:', err);\n      setError('Failed to load scholarships. Please try again later.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle page change\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    // Scroll to top when page changes\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <div className=\"bg-white min-h-screen\">\n      {/* Hero Section */}\n      <div className=\"relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden\">\n        {/* Background overlay */}\n        <div className=\"absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply\" />\n\n        <div className=\"max-w-7xl mx-auto py-20 px-4 sm:py-24 sm:px-6 lg:px-8 relative z-10\">\n          <div className=\"text-center\">\n            <div className=\"inline-flex items-center px-4 py-2 bg-blue-500/20 rounded-full text-blue-100 text-sm font-medium mb-6 animate-fade-in\">\n              <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse\"></span>\n              {pagination.total} bourses disponibles\n            </div>\n\n            <h1 className=\"text-4xl font-extrabold text-white sm:text-5xl sm:tracking-tight lg:text-6xl animate-fade-in\">\n              Trouvez Votre\n              <span className=\"block text-yellow-300\">Bourse Idéale</span>\n            </h1>\n\n\n          </div>\n        </div>\n      </div>\n\n      {/* Hero Article Section - Professional Format */}\n      <div className=\"bg-gray-100 py-6 md:py-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <article>\n            {/* Main Title - Integrated */}\n            <div className=\"mb-6\">\n              <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-2\">\n                Toutes les Bourses d'Études Disponibles\n              </h1>\n              <p className=\"text-gray-600 text-base\">\n                Explorez notre collection complète d'opportunités de financement pour tous les niveaux d'études\n              </p>\n            </div>\n\n            {/* Article Content - Full Width */}\n            <div className=\"prose max-w-none\">\n              <p className=\"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\">\n                Bienvenue sur la plateforme de référence pour les bourses d'études internationales. Cette page centralise toutes les opportunités de financement disponibles, soigneusement vérifiées et mises à jour quotidiennement pour vous offrir les meilleures chances de réussite dans vos projets académiques.\n              </p>\n\n              <p className=\"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\">\n                Vous trouverez ici des bourses pour tous les niveaux d'études - de la licence au doctorat - proposées par des gouvernements, des universités prestigieuses, des fondations internationales et des organisations privées. Chaque opportunité est accompagnée d'informations détaillées sur les critères d'éligibilité, les avantages financiers, les procédures de candidature et les échéances importantes.\n              </p>\n\n              <p className=\"text-gray-700 leading-relaxed text-sm md:text-base text-justify\">\n                Utilisez les filtres avancés ci-dessous pour affiner votre recherche selon votre niveau d'études, votre pays de destination préféré, votre domaine d'études ou le type de financement souhaité. Notre système de recherche intelligent vous permettra de découvrir rapidement les bourses qui correspondent parfaitement à votre profil et à vos ambitions académiques.\n              </p>\n            </div>\n          </article>\n        </div>\n      </div>\n\n      {/* Ad Placement - Top Banner */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block\">\n        <AdPlacement\n          adSlot=\"1234567890\"\n          adSize=\"leaderboard\"\n          responsive={true}\n          fullWidth={true}\n        />\n      </div>\n\n      {/* Search and Filter Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-10\">\n        <StandardizedFilters\n          searchQuery={searchQuery}\n          selectedLevel={selectedLevel}\n          selectedCountry={selectedCountry}\n          selectedStatus={selectedStatus}\n          onSearchChange={(query) => {\n            setSearchQuery(query);\n            setPagination(prev => ({ ...prev, page: 1 }));\n          }}\n          onLevelChange={(level) => {\n            setSelectedLevel(level);\n            setPagination(prev => ({ ...prev, page: 1 }));\n          }}\n          onCountryChange={(country) => {\n            setSelectedCountry(country);\n            setPagination(prev => ({ ...prev, page: 1 }));\n          }}\n          onStatusChange={(status) => {\n            setSelectedStatus(status);\n            setPagination(prev => ({ ...prev, page: 1 }));\n          }}\n          onReset={() => {\n            setSearchQuery('');\n            setSelectedLevel('');\n            setSelectedCountry('');\n            setSelectedStatus('');\n            setPagination(prev => ({ ...prev, page: 1 }));\n          }}\n          className=\"mb-12 transform translate-y-0 animate-slide-up\"\n        />\n      </div>\n\n      {/* Content Section */}\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16\">\n        {/* Main Content - Full Width */}\n        <div className=\"w-full\">\n        {/* Results header */}\n        <div className=\"flex flex-col md:flex-row justify-between items-start md:items-center mb-8\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-2\">Résultats de recherche</h2>\n            <p className=\"text-gray-600\">\n              {!loading && !error && `${pagination.total} bourses trouvées`}\n            </p>\n          </div>\n          <div className=\"mt-4 md:mt-0\">\n            <select\n              className=\"rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm\"\n            >\n              <option value=\"recent\">Plus récentes</option>\n              <option value=\"deadline\">Date limite proche</option>\n              <option value=\"relevance\">Pertinence</option>\n            </select>\n          </div>\n        </div>\n\n        {loading ? (\n          <div className=\"flex justify-center items-center py-16\">\n            <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n          </div>\n        ) : error ? (\n          <Alert\n            message=\"Erreur\"\n            description={error}\n            type=\"error\"\n            showIcon\n            className=\"mb-6 rounded-xl shadow-md\"\n          />\n        ) : (\n          <>\n            {/* Mobile Ad - Only visible on small screens */}\n            <div className=\"mb-8 md:hidden\">\n              <AdPlacement\n                adSlot=\"2345678901\"\n                adSize=\"rectangle\"\n                responsive={true}\n                fullWidth={true}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3\">\n              {scholarships.map((scholarship, index) => (\n                <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                  <EnhancedScholarshipCard\n                    id={scholarship.id}\n                    title={scholarship.title}\n                    thumbnail={scholarship.thumbnail}\n                    deadline={scholarship.deadline}\n                    isOpen={scholarship.isOpen}\n                    level={scholarship.level}\n                    country={scholarship.country}\n                    onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}\n                    index={index}\n                  />\n                </div>\n              ))}\n            </div>\n\n            {/* No Results Message */}\n            {scholarships.length === 0 && (\n              <div className=\"text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100\">\n                <svg className=\"mx-auto h-12 w-12 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                <h3 className=\"mt-4 text-lg font-medium text-gray-900\">Aucune bourse trouvée</h3>\n                <p className=\"mt-2 text-sm text-gray-500 max-w-md mx-auto\">\n                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.\n                </p>\n                <button\n                  onClick={() => {\n                    setSearchQuery('');\n                    setSelectedLevel('');\n                    setSelectedCountry('');\n                    setPagination(prev => ({ ...prev, page: 1 }));\n                  }}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n                >\n                  Réinitialiser les filtres\n                </button>\n              </div>\n            )}\n\n            {/* Pagination */}\n            {pagination.total > 0 && (\n              <div className=\"flex justify-center mt-12\">\n                <Pagination\n                  current={pagination.page}\n                  total={pagination.total}\n                  pageSize={pagination.limit}\n                  onChange={handlePageChange}\n                  showSizeChanger={false}\n                  showQuickJumper\n                  showTotal={(total) => `Total ${total} bourses`}\n                  className=\"shadow-sm rounded-xl p-2 bg-white\"\n                />\n              </div>\n            )}\n          </>\n        )}\n        </div>\n      </div>\n\n        {/* Page End Suggestions */}\n        <PageEndSuggestions\n          currentPageType=\"scholarship\"\n          currentItem={selectedLevel || selectedCountry}\n        />\n      </div>\n    );\n};\n\nexport default Scholarships;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC9C,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,OAAOC,uBAAuB,MAAM,uCAAuC;AAE3E,OAAOC,kBAAkB,MAAM,kCAAkC;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,mBAAmB,MAAM,2CAA2C;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAsB5E,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACY,WAAW,EAAEC,cAAc,CAAC,GAAGlB,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACqB,eAAe,EAAEC,kBAAkB,CAAC,GAAGtB,QAAQ,CAAC,EAAE,CAAC;EAC1D,MAAM,CAACuB,cAAc,EAAEC,iBAAiB,CAAC,GAAGxB,QAAQ,CAAC,EAAE,CAAC;EACxD,MAAM,CAACyB,YAAY,EAAEC,eAAe,CAAC,GAAG1B,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAAC2B,OAAO,EAAEC,UAAU,CAAC,GAAG5B,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAAC6B,KAAK,EAAEC,QAAQ,CAAC,GAAG9B,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAiB;IAC3DiC,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IAAE;IACVC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACArC,SAAS,CAAC,MAAM;IACd,MAAMsC,YAAY,GAAG,IAAIC,eAAe,CAACxB,QAAQ,CAACyB,MAAM,CAAC;IACzD,MAAMC,UAAU,GAAGH,YAAY,CAACI,GAAG,CAAC,OAAO,CAAC;IAC5C,MAAMC,YAAY,GAAGL,YAAY,CAACI,GAAG,CAAC,SAAS,CAAC;IAEhD,IAAID,UAAU,EAAE;MACdtB,gBAAgB,CAACsB,UAAU,CAAC;IAC9B;IACA,IAAIE,YAAY,EAAE;MAChBtB,kBAAkB,CAACsB,YAAY,CAAC;IAClC;EACF,CAAC,EAAE,CAAC5B,QAAQ,CAACyB,MAAM,CAAC,CAAC;;EAErB;EACAxC,SAAS,CAAC,MAAM;IACd4C,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACd,UAAU,CAACG,IAAI,EAAEf,aAAa,EAAEE,eAAe,EAAEJ,WAAW,CAAC,CAAC;EAElE,MAAM4B,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMkB,MAAM,GAAG,IAAIN,eAAe,CAAC,CAAC;MACpCM,MAAM,CAACC,MAAM,CAAC,MAAM,EAAEhB,UAAU,CAACG,IAAI,CAACc,QAAQ,CAAC,CAAC,CAAC;MACjDF,MAAM,CAACC,MAAM,CAAC,OAAO,EAAEhB,UAAU,CAACI,KAAK,CAACa,QAAQ,CAAC,CAAC,CAAC;MAEnD,IAAI/B,WAAW,EAAE;QACf6B,MAAM,CAACC,MAAM,CAAC,GAAG,EAAE9B,WAAW,CAAC;MACjC;MAEA,IAAIE,aAAa,EAAE;QACjB2B,MAAM,CAACC,MAAM,CAAC,OAAO,EAAE5B,aAAa,CAAC;MACvC;MAEA,IAAIE,eAAe,EAAE;QACnByB,MAAM,CAACC,MAAM,CAAC,SAAS,EAAE1B,eAAe,CAAC;MAC3C;MAEA,MAAM4B,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,4BAA4BP,MAAM,CAACE,QAAQ,CAAC,CAAC,EAAE,CAAC;MAExI,IAAI,CAACC,QAAQ,CAACK,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAEH,IAAI,CAAC;;MAEtD;MACA,MAAMI,gBAAgB,GAAGJ,IAAI,CAACA,IAAI,IAAIA,IAAI,CAAC/B,YAAY,IAAI,EAAE;MAC7D,MAAMoC,cAAc,GAAGL,IAAI,CAACzB,UAAU,IAAI,CAAC,CAAC;MAE5CL,eAAe,CAACkC,gBAAgB,CAAC;MACjC5B,aAAa,CAAC6B,cAAc,IAAI;QAC9B5B,KAAK,EAAE2B,gBAAgB,CAACE,MAAM,IAAI,CAAC;QACnC5B,IAAI,EAAE,CAAC;QACPC,KAAK,EAAE,CAAC;QACRC,UAAU,EAAE2B,IAAI,CAACC,IAAI,CAAC,CAACJ,gBAAgB,CAACE,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;QACzDzB,WAAW,EAAE,KAAK;QAClBC,eAAe,EAAE;MACnB,CAAC,CAAC;MACFR,QAAQ,CAAC,IAAI,CAAC;IAChB,CAAC,CAAC,OAAOmC,GAAG,EAAE;MACZP,OAAO,CAAC7B,KAAK,CAAC,8BAA8B,EAAEoC,GAAG,CAAC;MAClDnC,QAAQ,CAAC,sDAAsD,CAAC;MAChEJ,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAIhC,IAAY,IAAK;IACzCF,aAAa,CAACmC,IAAI,KAAK;MACrB,GAAGA,IAAI;MACPjC;IACF,CAAC,CAAC,CAAC;IACH;IACAkC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,oBACE5D,OAAA;IAAK6D,SAAS,EAAC,uBAAuB;IAAAC,QAAA,gBAEpC9D,OAAA;MAAK6D,SAAS,EAAC,sFAAsF;MAAAC,QAAA,gBAEnG9D,OAAA;QAAK6D,SAAS,EAAC;MAAyF;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE3GlE,OAAA;QAAK6D,SAAS,EAAC,qEAAqE;QAAAC,QAAA,eAClF9D,OAAA;UAAK6D,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1B9D,OAAA;YAAK6D,SAAS,EAAC,uHAAuH;YAAAC,QAAA,gBACpI9D,OAAA;cAAM6D,SAAS,EAAC;YAAsD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,EAC7E9C,UAAU,CAACE,KAAK,EAAC,sBACpB;UAAA;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAENlE,OAAA;YAAI6D,SAAS,EAAC,8FAA8F;YAAAC,QAAA,GAAC,eAE3G,eAAA9D,OAAA;cAAM6D,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,0BAA0B;MAAAC,QAAA,eACvC9D,OAAA;QAAK6D,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrD9D,OAAA;UAAA8D,QAAA,gBAEE9D,OAAA;YAAK6D,SAAS,EAAC,MAAM;YAAAC,QAAA,gBACnB9D,OAAA;cAAI6D,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAAC;YAElE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLlE,OAAA;cAAG6D,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAEvC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eAGNlE,OAAA;YAAK6D,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC/B9D,OAAA;cAAG6D,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAEpF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJlE,OAAA;cAAG6D,SAAS,EAAC,sEAAsE;cAAAC,QAAA,EAAC;YAEpF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJlE,OAAA;cAAG6D,SAAS,EAAC,iEAAiE;cAAAC,QAAA,EAAC;YAE/E;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,mEAAmE;MAAAC,QAAA,eAChF9D,OAAA,CAACH,WAAW;QACVsE,MAAM,EAAC,YAAY;QACnBC,MAAM,EAAC,aAAa;QACpBC,UAAU,EAAE,IAAK;QACjBC,SAAS,EAAE;MAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5D9D,OAAA,CAACF,mBAAmB;QAClBQ,WAAW,EAAEA,WAAY;QACzBE,aAAa,EAAEA,aAAc;QAC7BE,eAAe,EAAEA,eAAgB;QACjCE,cAAc,EAAEA,cAAe;QAC/B2D,cAAc,EAAGC,KAAK,IAAK;UACzBjE,cAAc,CAACiE,KAAK,CAAC;UACrBnD,aAAa,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,IAAI,EAAE;UAAE,CAAC,CAAC,CAAC;QAC/C,CAAE;QACFkD,aAAa,EAAGC,KAAK,IAAK;UACxBjE,gBAAgB,CAACiE,KAAK,CAAC;UACvBrD,aAAa,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,IAAI,EAAE;UAAE,CAAC,CAAC,CAAC;QAC/C,CAAE;QACFoD,eAAe,EAAGC,OAAO,IAAK;UAC5BjE,kBAAkB,CAACiE,OAAO,CAAC;UAC3BvD,aAAa,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,IAAI,EAAE;UAAE,CAAC,CAAC,CAAC;QAC/C,CAAE;QACFsD,cAAc,EAAGC,MAAM,IAAK;UAC1BjE,iBAAiB,CAACiE,MAAM,CAAC;UACzBzD,aAAa,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,IAAI,EAAE;UAAE,CAAC,CAAC,CAAC;QAC/C,CAAE;QACFwD,OAAO,EAAEA,CAAA,KAAM;UACbxE,cAAc,CAAC,EAAE,CAAC;UAClBE,gBAAgB,CAAC,EAAE,CAAC;UACpBE,kBAAkB,CAAC,EAAE,CAAC;UACtBE,iBAAiB,CAAC,EAAE,CAAC;UACrBQ,aAAa,CAACmC,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEjC,IAAI,EAAE;UAAE,CAAC,CAAC,CAAC;QAC/C,CAAE;QACFsC,SAAS,EAAC;MAAgD;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNlE,OAAA;MAAK6D,SAAS,EAAC,8CAA8C;MAAAC,QAAA,eAE3D9D,OAAA;QAAK6D,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBAEvB9D,OAAA;UAAK6D,SAAS,EAAC,4EAA4E;UAAAC,QAAA,gBACzF9D,OAAA;YAAA8D,QAAA,gBACE9D,OAAA;cAAI6D,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFlE,OAAA;cAAG6D,SAAS,EAAC,eAAe;cAAAC,QAAA,EACzB,CAAC9C,OAAO,IAAI,CAACE,KAAK,IAAI,GAAGE,UAAU,CAACE,KAAK;YAAmB;cAAAyC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,eACNlE,OAAA;YAAK6D,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B9D,OAAA;cACE6D,SAAS,EAAC,mIAAmI;cAAAC,QAAA,gBAE7I9D,OAAA;gBAAQgF,KAAK,EAAC,QAAQ;gBAAAlB,QAAA,EAAC;cAAa;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAC7ClE,OAAA;gBAAQgF,KAAK,EAAC,UAAU;gBAAAlB,QAAA,EAAC;cAAkB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACpDlE,OAAA;gBAAQgF,KAAK,EAAC,WAAW;gBAAAlB,QAAA,EAAC;cAAU;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAELlD,OAAO,gBACNhB,OAAA;UAAK6D,SAAS,EAAC,wCAAwC;UAAAC,QAAA,eACrD9D,OAAA,CAACR,IAAI;YAACyF,IAAI,EAAC,OAAO;YAACC,GAAG,EAAC;UAA2B;YAAAnB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClD,CAAC,GACJhD,KAAK,gBACPlB,OAAA,CAACP,KAAK;UACJ0F,OAAO,EAAC,QAAQ;UAChBC,WAAW,EAAElE,KAAM;UACnBmE,IAAI,EAAC,OAAO;UACZC,QAAQ;UACRzB,SAAS,EAAC;QAA2B;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtC,CAAC,gBAEFlE,OAAA,CAAAE,SAAA;UAAA4D,QAAA,gBAEE9D,OAAA;YAAK6D,SAAS,EAAC,gBAAgB;YAAAC,QAAA,eAC7B9D,OAAA,CAACH,WAAW;cACVsE,MAAM,EAAC,YAAY;cACnBC,MAAM,EAAC,WAAW;cAClBC,UAAU,EAAE,IAAK;cACjBC,SAAS,EAAE;YAAK;cAAAP,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAENlE,OAAA;YAAK6D,SAAS,EAAC,sDAAsD;YAAAC,QAAA,EAClEhD,YAAY,CAACyE,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,kBACnCzF,OAAA;cAA0B6D,SAAS,EAAC,iBAAiB;cAAC6B,KAAK,EAAE;gBAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;cAAI,CAAE;cAAA3B,QAAA,eACjG9D,OAAA,CAACL,uBAAuB;gBACtBiG,EAAE,EAAEJ,WAAW,CAACI,EAAG;gBACnBC,KAAK,EAAEL,WAAW,CAACK,KAAM;gBACzBC,SAAS,EAAEN,WAAW,CAACM,SAAU;gBACjCC,QAAQ,EAAEP,WAAW,CAACO,QAAS;gBAC/BC,MAAM,EAAER,WAAW,CAACQ,MAAO;gBAC3BtB,KAAK,EAAEc,WAAW,CAACd,KAAM;gBACzBE,OAAO,EAAEY,WAAW,CAACZ,OAAQ;gBAC7BqB,OAAO,EAAEA,CAACL,EAAE,EAAEM,IAAI,KAAKzC,MAAM,CAACpD,QAAQ,CAAC8F,IAAI,GAAGD,IAAI,GAAG,WAAWA,IAAI,EAAE,GAAG,iBAAiBN,EAAE,EAAG;gBAC/FH,KAAK,EAAEA;cAAM;gBAAA1B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACd;YAAC,GAXMsB,WAAW,CAACI,EAAE;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAYnB,CACN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,EAGLpD,YAAY,CAACqC,MAAM,KAAK,CAAC,iBACxBnD,OAAA;YAAK6D,SAAS,EAAC,2EAA2E;YAAAC,QAAA,gBACxF9D,OAAA;cAAK6D,SAAS,EAAC,iCAAiC;cAACuC,IAAI,EAAC,MAAM;cAACC,OAAO,EAAC,WAAW;cAACC,MAAM,EAAC,cAAc;cAAAxC,QAAA,eACpG9D,OAAA;gBAAMuG,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,GAAI;gBAACC,CAAC,EAAC;cAAoF;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3J,CAAC,eACNlE,OAAA;cAAI6D,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EAAC;YAAqB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjFlE,OAAA;cAAG6D,SAAS,EAAC,6CAA6C;cAAAC,QAAA,EAAC;YAE3D;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJlE,OAAA;cACEiG,OAAO,EAAEA,CAAA,KAAM;gBACb1F,cAAc,CAAC,EAAE,CAAC;gBAClBE,gBAAgB,CAAC,EAAE,CAAC;gBACpBE,kBAAkB,CAAC,EAAE,CAAC;gBACtBU,aAAa,CAACmC,IAAI,KAAK;kBAAE,GAAGA,IAAI;kBAAEjC,IAAI,EAAE;gBAAE,CAAC,CAAC,CAAC;cAC/C,CAAE;cACFsC,SAAS,EAAC,+NAA+N;cAAAC,QAAA,EAC1O;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CACN,EAGA9C,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBtB,OAAA;YAAK6D,SAAS,EAAC,2BAA2B;YAAAC,QAAA,eACxC9D,OAAA,CAACT,UAAU;cACToH,OAAO,EAAEvF,UAAU,CAACG,IAAK;cACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;cACxBsF,QAAQ,EAAExF,UAAU,CAACI,KAAM;cAC3BqF,QAAQ,EAAEtD,gBAAiB;cAC3BuD,eAAe,EAAE,KAAM;cACvBC,eAAe;cACfC,SAAS,EAAG1F,KAAK,IAAK,SAASA,KAAK,UAAW;cAC/CuC,SAAS,EAAC;YAAmC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN;QAAA,eACD,CACH;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGJlE,OAAA,CAACJ,kBAAkB;MACjBqH,eAAe,EAAC,aAAa;MAC7BC,WAAW,EAAE1G,aAAa,IAAIE;IAAgB;MAAAqD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC/C,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEZ,CAAC;AAAC9D,EAAA,CA5TID,YAAsB;EAAA,QACTT,WAAW;AAAA;AAAAyH,EAAA,GADxBhH,YAAsB;AA8T5B,eAAeA,YAAY;AAAC,IAAAgH,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}