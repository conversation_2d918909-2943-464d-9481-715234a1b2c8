{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/CommentsSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Avatar, message, Spin } from 'antd';\nimport { UserOutlined, SendOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  TextArea\n} = Input;\nconst CommentsSection = ({\n  pageType,\n  pageId\n}) => {\n  _s();\n  const [comments, setComments] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [newComment, setNewComment] = useState('');\n  const [userName, setUserName] = useState('');\n  const [replyingTo, setReplyingTo] = useState(null);\n  const [replyContent, setReplyContent] = useState('');\n  useEffect(() => {\n    fetchComments();\n  }, [pageType, pageId]);\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments/${pageType}/${pageId}`);\n      if (response.ok) {\n        const data = await response.json();\n        setComments(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching comments:', error);\n      // Set some mock data for demonstration\n      setComments([{\n        id: 1,\n        author: 'Ahmed Benali',\n        content: 'Bonjour, j\\'aimerais savoir quelles sont les conditions d\\'éligibilité pour les bourses de licence au Canada?',\n        timestamp: '2024-01-15T10:30:00Z',\n        isAdmin: false,\n        replies: [{\n          id: 2,\n          author: 'Admin MaBourse',\n          content: 'Bonjour Ahmed, pour les bourses de licence au Canada, vous devez généralement avoir un excellent dossier académique (moyenne supérieure à 16/20), une maîtrise de l\\'anglais ou du français selon la province, et parfois une expérience associative. Je vous recommande de consulter notre guide détaillé sur les bourses canadiennes.',\n          timestamp: '2024-01-15T14:20:00Z',\n          isAdmin: true\n        }]\n      }, {\n        id: 3,\n        author: 'Fatima Zahra',\n        content: 'Est-ce que les bourses couvrent aussi les frais de logement et de nourriture?',\n        timestamp: '2024-01-16T09:15:00Z',\n        isAdmin: false,\n        replies: [{\n          id: 4,\n          author: 'Admin MaBourse',\n          content: 'Bonjour Fatima, cela dépend du type de bourse. Les bourses complètes (fully funded) couvrent généralement les frais de scolarité, logement, nourriture et parfois même les frais de voyage. Les bourses partielles ne couvrent qu\\'une partie de ces frais. Chaque bourse a ses propres conditions que vous pouvez consulter dans la description détaillée.',\n          timestamp: '2024-01-16T11:45:00Z',\n          isAdmin: true\n        }]\n      }]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleSubmitComment = async () => {\n    if (!newComment.trim() || !userName.trim()) {\n      message.error('Veuillez remplir tous les champs');\n      return;\n    }\n    setSubmitting(true);\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          pageType,\n          pageId,\n          author: userName,\n          content: newComment\n        })\n      });\n      if (response.ok) {\n        message.success('Commentaire ajouté avec succès!');\n        setNewComment('');\n        fetchComments(); // Refresh comments\n      } else {\n        message.error('Erreur lors de l\\'ajout du commentaire');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n      message.error('Erreur lors de l\\'ajout du commentaire');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const handleSubmitReply = async parentId => {\n    if (!replyContent.trim() || !userName.trim()) {\n      message.error('Veuillez remplir tous les champs');\n      return;\n    }\n    setSubmitting(true);\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments/reply`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json'\n        },\n        body: JSON.stringify({\n          parentId,\n          author: userName,\n          content: replyContent\n        })\n      });\n      if (response.ok) {\n        message.success('Réponse ajoutée avec succès!');\n        setReplyContent('');\n        setReplyingTo(null);\n        fetchComments(); // Refresh comments\n      } else {\n        message.error('Erreur lors de l\\'ajout de la réponse');\n      }\n    } catch (error) {\n      console.error('Error submitting reply:', error);\n      message.error('Erreur lors de l\\'ajout de la réponse');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n  const formatDate = timestamp => {\n    return new Date(timestamp).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-xl shadow-lg p-8\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: /*#__PURE__*/_jsxDEV(Spin, {\n          size: \"large\",\n          tip: \"Chargement des commentaires...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 174,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 172,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl shadow-lg p-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n        className: \"text-2xl font-bold text-gray-900 mb-2\",\n        children: \"Discussion et Questions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"text-gray-600\",\n        children: \"Posez vos questions sur les bourses d'\\xE9tudes. Notre \\xE9quipe vous r\\xE9pondra rapidement.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 186,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 182,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8 p-6 bg-gray-50 rounded-xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n        className: \"text-lg font-semibold text-gray-900 mb-4\",\n        children: \"Poser une question\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 193,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(Input, {\n          placeholder: \"Votre nom\",\n          value: userName,\n          onChange: e => setUserName(e.target.value),\n          prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 201,\n            columnNumber: 21\n          }, this),\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 197,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n          placeholder: \"Votre question ou commentaire...\",\n          value: newComment,\n          onChange: e => setNewComment(e.target.value),\n          rows: 4,\n          size: \"large\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 204,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"primary\",\n          icon: /*#__PURE__*/_jsxDEV(SendOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 213,\n            columnNumber: 19\n          }, this),\n          onClick: handleSubmitComment,\n          loading: submitting,\n          size: \"large\",\n          className: \"bg-blue-600 hover:bg-blue-700\",\n          children: \"Publier la question\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 211,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 196,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 192,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"space-y-6\",\n      children: comments.length > 0 ? comments.map(comment => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"border-l-4 border-blue-200 pl-6\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-start space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(Avatar, {\n            icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 231,\n              columnNumber: 25\n            }, this),\n            className: comment.isAdmin ? 'bg-green-600' : 'bg-blue-600'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 230,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-2 mb-2\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"font-semibold text-gray-900\",\n                children: comment.author\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 21\n              }, this), comment.isAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n                children: \"Admin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm text-gray-500\",\n                children: formatDate(comment.timestamp)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 244,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 235,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-700 mb-3 leading-relaxed\",\n              children: comment.content\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 19\n            }, this), comment.replies && comment.replies.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 space-y-4\",\n              children: comment.replies.map(reply => /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-start space-x-3 bg-gray-50 p-4 rounded-lg\",\n                children: [/*#__PURE__*/_jsxDEV(Avatar, {\n                  size: \"small\",\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 259,\n                    columnNumber: 35\n                  }, this),\n                  className: reply.isAdmin ? 'bg-green-600' : 'bg-gray-600'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 257,\n                  columnNumber: 27\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 mb-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-medium text-gray-900 text-sm\",\n                      children: reply.author\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 264,\n                      columnNumber: 31\n                    }, this), reply.isAdmin && /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\",\n                      children: \"Admin\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 268,\n                      columnNumber: 33\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-xs text-gray-500\",\n                      children: formatDate(reply.timestamp)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 272,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 263,\n                    columnNumber: 29\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-gray-700 text-sm leading-relaxed\",\n                    children: reply.content\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 276,\n                    columnNumber: 29\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 262,\n                  columnNumber: 27\n                }, this)]\n              }, reply.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 256,\n                columnNumber: 25\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 254,\n              columnNumber: 21\n            }, this), replyingTo === comment.id ? /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-3\",\n                children: [/*#__PURE__*/_jsxDEV(Input, {\n                  placeholder: \"Votre nom\",\n                  value: userName,\n                  onChange: e => setUserName(e.target.value),\n                  prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 293,\n                    columnNumber: 35\n                  }, this),\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(TextArea, {\n                  placeholder: \"Votre r\\xE9ponse...\",\n                  value: replyContent,\n                  onChange: e => setReplyContent(e.target.value),\n                  rows: 3,\n                  size: \"small\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 25\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex space-x-2\",\n                  children: [/*#__PURE__*/_jsxDEV(Button, {\n                    type: \"primary\",\n                    size: \"small\",\n                    onClick: () => handleSubmitReply(comment.id),\n                    loading: submitting,\n                    className: \"bg-blue-600 hover:bg-blue-700\",\n                    children: \"R\\xE9pondre\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 304,\n                    columnNumber: 27\n                  }, this), /*#__PURE__*/_jsxDEV(Button, {\n                    size: \"small\",\n                    onClick: () => {\n                      setReplyingTo(null);\n                      setReplyContent('');\n                    },\n                    children: \"Annuler\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 313,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 288,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 287,\n              columnNumber: 21\n            }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mt-3\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                onClick: () => setReplyingTo(comment.id),\n                className: \"text-blue-600 hover:text-blue-700 p-0 h-auto\",\n                children: \"R\\xE9pondre \\xE0 ce commentaire\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 326,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 234,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 229,\n          columnNumber: 15\n        }, this)\n      }, comment.id, false, {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 13\n      }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center py-12\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-gray-400 mb-4\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"w-16 h-16 mx-auto\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 1.5,\n              d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 343,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n          className: \"text-lg font-medium text-gray-900 mb-2\",\n          children: \"Aucune question pour le moment\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-500\",\n          children: \"Soyez le premier \\xE0 poser une question sur cette page.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 351,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 342,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 181,\n    columnNumber: 5\n  }, this);\n};\n_s(CommentsSection, \"uHQyjhBOl8z6eWxR3l+vkD/epqU=\");\n_c = CommentsSection;\nexport default CommentsSection;\nvar _c;\n$RefreshReg$(_c, \"CommentsSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON>", "Input", "Avatar", "message", "Spin", "UserOutlined", "SendOutlined", "jsxDEV", "_jsxDEV", "TextArea", "CommentsSection", "pageType", "pageId", "_s", "comments", "setComments", "loading", "setLoading", "submitting", "setSubmitting", "newComment", "setNewComment", "userName", "setUserName", "replyingTo", "setReplyingTo", "replyContent", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fetchComments", "apiUrl", "process", "env", "REACT_APP_API_URL", "response", "fetch", "ok", "data", "json", "error", "console", "id", "author", "content", "timestamp", "isAdmin", "replies", "handleSubmitComment", "trim", "method", "headers", "body", "JSON", "stringify", "success", "handleSubmitReply", "parentId", "formatDate", "Date", "toLocaleDateString", "year", "month", "day", "hour", "minute", "className", "children", "size", "tip", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "placeholder", "value", "onChange", "e", "target", "prefix", "rows", "type", "icon", "onClick", "length", "map", "comment", "reply", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/CommentsSection.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Button, Input, Avatar, message, Spin } from 'antd';\nimport { UserOutlined, SendOutlined } from '@ant-design/icons';\n\nconst { TextArea } = Input;\n\ninterface Comment {\n  id: number;\n  author: string;\n  content: string;\n  timestamp: string;\n  isAdmin: boolean;\n  replies?: Comment[];\n}\n\ninterface CommentsSectionProps {\n  pageType: string;\n  pageId: string;\n}\n\nconst CommentsSection: React.FC<CommentsSectionProps> = ({ pageType, pageId }) => {\n  const [comments, setComments] = useState<Comment[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [submitting, setSubmitting] = useState(false);\n  const [newComment, setNewComment] = useState('');\n  const [userName, setUserName] = useState('');\n  const [replyingTo, setReplyingTo] = useState<number | null>(null);\n  const [replyContent, setReplyContent] = useState('');\n\n  useEffect(() => {\n    fetchComments();\n  }, [pageType, pageId]);\n\n  const fetchComments = async () => {\n    try {\n      setLoading(true);\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments/${pageType}/${pageId}`);\n      \n      if (response.ok) {\n        const data = await response.json();\n        setComments(data.data || []);\n      }\n    } catch (error) {\n      console.error('Error fetching comments:', error);\n      // Set some mock data for demonstration\n      setComments([\n        {\n          id: 1,\n          author: 'Ahmed Benali',\n          content: 'Bonjour, j\\'aimerais savoir quelles sont les conditions d\\'éligibilité pour les bourses de licence au Canada?',\n          timestamp: '2024-01-15T10:30:00Z',\n          isAdmin: false,\n          replies: [\n            {\n              id: 2,\n              author: 'Admin MaBourse',\n              content: 'Bonjour Ahmed, pour les bourses de licence au Canada, vous devez généralement avoir un excellent dossier académique (moyenne supérieure à 16/20), une maîtrise de l\\'anglais ou du français selon la province, et parfois une expérience associative. Je vous recommande de consulter notre guide détaillé sur les bourses canadiennes.',\n              timestamp: '2024-01-15T14:20:00Z',\n              isAdmin: true\n            }\n          ]\n        },\n        {\n          id: 3,\n          author: 'Fatima Zahra',\n          content: 'Est-ce que les bourses couvrent aussi les frais de logement et de nourriture?',\n          timestamp: '2024-01-16T09:15:00Z',\n          isAdmin: false,\n          replies: [\n            {\n              id: 4,\n              author: 'Admin MaBourse',\n              content: 'Bonjour Fatima, cela dépend du type de bourse. Les bourses complètes (fully funded) couvrent généralement les frais de scolarité, logement, nourriture et parfois même les frais de voyage. Les bourses partielles ne couvrent qu\\'une partie de ces frais. Chaque bourse a ses propres conditions que vous pouvez consulter dans la description détaillée.',\n              timestamp: '2024-01-16T11:45:00Z',\n              isAdmin: true\n            }\n          ]\n        }\n      ]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleSubmitComment = async () => {\n    if (!newComment.trim() || !userName.trim()) {\n      message.error('Veuillez remplir tous les champs');\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          pageType,\n          pageId,\n          author: userName,\n          content: newComment\n        }),\n      });\n\n      if (response.ok) {\n        message.success('Commentaire ajouté avec succès!');\n        setNewComment('');\n        fetchComments(); // Refresh comments\n      } else {\n        message.error('Erreur lors de l\\'ajout du commentaire');\n      }\n    } catch (error) {\n      console.error('Error submitting comment:', error);\n      message.error('Erreur lors de l\\'ajout du commentaire');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const handleSubmitReply = async (parentId: number) => {\n    if (!replyContent.trim() || !userName.trim()) {\n      message.error('Veuillez remplir tous les champs');\n      return;\n    }\n\n    setSubmitting(true);\n    try {\n      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5001';\n      const response = await fetch(`${apiUrl}/api/comments/reply`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          parentId,\n          author: userName,\n          content: replyContent\n        }),\n      });\n\n      if (response.ok) {\n        message.success('Réponse ajoutée avec succès!');\n        setReplyContent('');\n        setReplyingTo(null);\n        fetchComments(); // Refresh comments\n      } else {\n        message.error('Erreur lors de l\\'ajout de la réponse');\n      }\n    } catch (error) {\n      console.error('Error submitting reply:', error);\n      message.error('Erreur lors de l\\'ajout de la réponse');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  const formatDate = (timestamp: string) => {\n    return new Date(timestamp).toLocaleDateString('fr-FR', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white rounded-xl shadow-lg p-8\">\n        <div className=\"text-center\">\n          <Spin size=\"large\" tip=\"Chargement des commentaires...\" />\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"bg-white rounded-xl shadow-lg p-8\">\n      <div className=\"mb-8\">\n        <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n          Discussion et Questions\n        </h3>\n        <p className=\"text-gray-600\">\n          Posez vos questions sur les bourses d'études. Notre équipe vous répondra rapidement.\n        </p>\n      </div>\n\n      {/* Add New Comment */}\n      <div className=\"mb-8 p-6 bg-gray-50 rounded-xl\">\n        <h4 className=\"text-lg font-semibold text-gray-900 mb-4\">\n          Poser une question\n        </h4>\n        <div className=\"space-y-4\">\n          <Input\n            placeholder=\"Votre nom\"\n            value={userName}\n            onChange={(e) => setUserName(e.target.value)}\n            prefix={<UserOutlined />}\n            size=\"large\"\n          />\n          <TextArea\n            placeholder=\"Votre question ou commentaire...\"\n            value={newComment}\n            onChange={(e) => setNewComment(e.target.value)}\n            rows={4}\n            size=\"large\"\n          />\n          <Button\n            type=\"primary\"\n            icon={<SendOutlined />}\n            onClick={handleSubmitComment}\n            loading={submitting}\n            size=\"large\"\n            className=\"bg-blue-600 hover:bg-blue-700\"\n          >\n            Publier la question\n          </Button>\n        </div>\n      </div>\n\n      {/* Comments List */}\n      <div className=\"space-y-6\">\n        {comments.length > 0 ? (\n          comments.map((comment) => (\n            <div key={comment.id} className=\"border-l-4 border-blue-200 pl-6\">\n              <div className=\"flex items-start space-x-4\">\n                <Avatar\n                  icon={<UserOutlined />}\n                  className={comment.isAdmin ? 'bg-green-600' : 'bg-blue-600'}\n                />\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center space-x-2 mb-2\">\n                    <span className=\"font-semibold text-gray-900\">\n                      {comment.author}\n                    </span>\n                    {comment.isAdmin && (\n                      <span className=\"px-2 py-1 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                        Admin\n                      </span>\n                    )}\n                    <span className=\"text-sm text-gray-500\">\n                      {formatDate(comment.timestamp)}\n                    </span>\n                  </div>\n                  <p className=\"text-gray-700 mb-3 leading-relaxed\">\n                    {comment.content}\n                  </p>\n                  \n                  {/* Replies */}\n                  {comment.replies && comment.replies.length > 0 && (\n                    <div className=\"mt-4 space-y-4\">\n                      {comment.replies.map((reply) => (\n                        <div key={reply.id} className=\"flex items-start space-x-3 bg-gray-50 p-4 rounded-lg\">\n                          <Avatar\n                            size=\"small\"\n                            icon={<UserOutlined />}\n                            className={reply.isAdmin ? 'bg-green-600' : 'bg-gray-600'}\n                          />\n                          <div className=\"flex-1\">\n                            <div className=\"flex items-center space-x-2 mb-1\">\n                              <span className=\"font-medium text-gray-900 text-sm\">\n                                {reply.author}\n                              </span>\n                              {reply.isAdmin && (\n                                <span className=\"px-2 py-0.5 bg-green-100 text-green-800 text-xs font-medium rounded-full\">\n                                  Admin\n                                </span>\n                              )}\n                              <span className=\"text-xs text-gray-500\">\n                                {formatDate(reply.timestamp)}\n                              </span>\n                            </div>\n                            <p className=\"text-gray-700 text-sm leading-relaxed\">\n                              {reply.content}\n                            </p>\n                          </div>\n                        </div>\n                      ))}\n                    </div>\n                  )}\n\n                  {/* Reply Form */}\n                  {replyingTo === comment.id ? (\n                    <div className=\"mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200\">\n                      <div className=\"space-y-3\">\n                        <Input\n                          placeholder=\"Votre nom\"\n                          value={userName}\n                          onChange={(e) => setUserName(e.target.value)}\n                          prefix={<UserOutlined />}\n                          size=\"small\"\n                        />\n                        <TextArea\n                          placeholder=\"Votre réponse...\"\n                          value={replyContent}\n                          onChange={(e) => setReplyContent(e.target.value)}\n                          rows={3}\n                          size=\"small\"\n                        />\n                        <div className=\"flex space-x-2\">\n                          <Button\n                            type=\"primary\"\n                            size=\"small\"\n                            onClick={() => handleSubmitReply(comment.id)}\n                            loading={submitting}\n                            className=\"bg-blue-600 hover:bg-blue-700\"\n                          >\n                            Répondre\n                          </Button>\n                          <Button\n                            size=\"small\"\n                            onClick={() => {\n                              setReplyingTo(null);\n                              setReplyContent('');\n                            }}\n                          >\n                            Annuler\n                          </Button>\n                        </div>\n                      </div>\n                    </div>\n                  ) : (\n                    <div className=\"mt-3\">\n                      <Button\n                        type=\"link\"\n                        size=\"small\"\n                        onClick={() => setReplyingTo(comment.id)}\n                        className=\"text-blue-600 hover:text-blue-700 p-0 h-auto\"\n                      >\n                        Répondre à ce commentaire\n                      </Button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))\n        ) : (\n          <div className=\"text-center py-12\">\n            <div className=\"text-gray-400 mb-4\">\n              <svg className=\"w-16 h-16 mx-auto\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n            </div>\n            <h4 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucune question pour le moment\n            </h4>\n            <p className=\"text-gray-500\">\n              Soyez le premier à poser une question sur cette page.\n            </p>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default CommentsSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,IAAI,QAAQ,MAAM;AAC3D,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/D,MAAM;EAAEC;AAAS,CAAC,GAAGR,KAAK;AAgB1B,MAAMS,eAA+C,GAAGA,CAAC;EAAEC,QAAQ;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAChF,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGnB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACoB,UAAU,EAAEC,aAAa,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACsB,UAAU,EAAEC,aAAa,CAAC,GAAGvB,QAAQ,CAAC,EAAE,CAAC;EAChD,MAAM,CAACwB,QAAQ,EAAEC,WAAW,CAAC,GAAGzB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAAC0B,UAAU,EAAEC,aAAa,CAAC,GAAG3B,QAAQ,CAAgB,IAAI,CAAC;EACjE,MAAM,CAAC4B,YAAY,EAAEC,eAAe,CAAC,GAAG7B,QAAQ,CAAC,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd6B,aAAa,CAAC,CAAC;EACjB,CAAC,EAAE,CAACjB,QAAQ,EAAEC,MAAM,CAAC,CAAC;EAEtB,MAAMgB,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACFX,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMY,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,iBAAiBlB,QAAQ,IAAIC,MAAM,EAAE,CAAC;MAE5E,IAAIqB,QAAQ,CAACE,EAAE,EAAE;QACf,MAAMC,IAAI,GAAG,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;QAClCtB,WAAW,CAACqB,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD;MACAvB,WAAW,CAAC,CACV;QACEyB,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,cAAc;QACtBC,OAAO,EAAE,+GAA+G;QACxHC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,CACP;UACEL,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,gBAAgB;UACxBC,OAAO,EAAE,yUAAyU;UAClVC,SAAS,EAAE,sBAAsB;UACjCC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC,EACD;QACEJ,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,cAAc;QACtBC,OAAO,EAAE,+EAA+E;QACxFC,SAAS,EAAE,sBAAsB;QACjCC,OAAO,EAAE,KAAK;QACdC,OAAO,EAAE,CACP;UACEL,EAAE,EAAE,CAAC;UACLC,MAAM,EAAE,gBAAgB;UACxBC,OAAO,EAAE,6VAA6V;UACtWC,SAAS,EAAE,sBAAsB;UACjCC,OAAO,EAAE;QACX,CAAC;MAEL,CAAC,CACF,CAAC;IACJ,CAAC,SAAS;MACR3B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM6B,mBAAmB,GAAG,MAAAA,CAAA,KAAY;IACtC,IAAI,CAAC1B,UAAU,CAAC2B,IAAI,CAAC,CAAC,IAAI,CAACzB,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAAE;MAC1C5C,OAAO,CAACmC,KAAK,CAAC,kCAAkC,CAAC;MACjD;IACF;IAEAnB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMU,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,eAAe,EAAE;QACrDmB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBzC,QAAQ;UACRC,MAAM;UACN6B,MAAM,EAAEnB,QAAQ;UAChBoB,OAAO,EAAEtB;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAIa,QAAQ,CAACE,EAAE,EAAE;QACfhC,OAAO,CAACkD,OAAO,CAAC,iCAAiC,CAAC;QAClDhC,aAAa,CAAC,EAAE,CAAC;QACjBO,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLzB,OAAO,CAACmC,KAAK,CAAC,wCAAwC,CAAC;MACzD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjDnC,OAAO,CAACmC,KAAK,CAAC,wCAAwC,CAAC;IACzD,CAAC,SAAS;MACRnB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMmC,iBAAiB,GAAG,MAAOC,QAAgB,IAAK;IACpD,IAAI,CAAC7B,YAAY,CAACqB,IAAI,CAAC,CAAC,IAAI,CAACzB,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAAE;MAC5C5C,OAAO,CAACmC,KAAK,CAAC,kCAAkC,CAAC;MACjD;IACF;IAEAnB,aAAa,CAAC,IAAI,CAAC;IACnB,IAAI;MACF,MAAMU,MAAM,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB;MACvE,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGL,MAAM,qBAAqB,EAAE;QAC3DmB,MAAM,EAAE,MAAM;QACdC,OAAO,EAAE;UACP,cAAc,EAAE;QAClB,CAAC;QACDC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACnBG,QAAQ;UACRd,MAAM,EAAEnB,QAAQ;UAChBoB,OAAO,EAAEhB;QACX,CAAC;MACH,CAAC,CAAC;MAEF,IAAIO,QAAQ,CAACE,EAAE,EAAE;QACfhC,OAAO,CAACkD,OAAO,CAAC,8BAA8B,CAAC;QAC/C1B,eAAe,CAAC,EAAE,CAAC;QACnBF,aAAa,CAAC,IAAI,CAAC;QACnBG,aAAa,CAAC,CAAC,CAAC,CAAC;MACnB,CAAC,MAAM;QACLzB,OAAO,CAACmC,KAAK,CAAC,uCAAuC,CAAC;MACxD;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/CnC,OAAO,CAACmC,KAAK,CAAC,uCAAuC,CAAC;IACxD,CAAC,SAAS;MACRnB,aAAa,CAAC,KAAK,CAAC;IACtB;EACF,CAAC;EAED,MAAMqC,UAAU,GAAIb,SAAiB,IAAK;IACxC,OAAO,IAAIc,IAAI,CAACd,SAAS,CAAC,CAACe,kBAAkB,CAAC,OAAO,EAAE;MACrDC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,MAAM;MACbC,GAAG,EAAE,SAAS;MACdC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;IACV,CAAC,CAAC;EACJ,CAAC;EAED,IAAI/C,OAAO,EAAE;IACX,oBACER,OAAA;MAAKwD,SAAS,EAAC,mCAAmC;MAAAC,QAAA,eAChDzD,OAAA;QAAKwD,SAAS,EAAC,aAAa;QAAAC,QAAA,eAC1BzD,OAAA,CAACJ,IAAI;UAAC8D,IAAI,EAAC,OAAO;UAACC,GAAG,EAAC;QAAgC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACE/D,OAAA;IAAKwD,SAAS,EAAC,mCAAmC;IAAAC,QAAA,gBAChDzD,OAAA;MAAKwD,SAAS,EAAC,MAAM;MAAAC,QAAA,gBACnBzD,OAAA;QAAIwD,SAAS,EAAC,uCAAuC;QAAAC,QAAA,EAAC;MAEtD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/D,OAAA;QAAGwD,SAAS,EAAC,eAAe;QAAAC,QAAA,EAAC;MAE7B;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC,eAGN/D,OAAA;MAAKwD,SAAS,EAAC,gCAAgC;MAAAC,QAAA,gBAC7CzD,OAAA;QAAIwD,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAEzD;QAAAG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACL/D,OAAA;QAAKwD,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACxBzD,OAAA,CAACP,KAAK;UACJuE,WAAW,EAAC,WAAW;UACvBC,KAAK,EAAEnD,QAAS;UAChBoD,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC7CI,MAAM,eAAErE,OAAA,CAACH,YAAY;YAAA+D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBL,IAAI,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACF/D,OAAA,CAACC,QAAQ;UACP+D,WAAW,EAAC,kCAAkC;UAC9CC,KAAK,EAAErD,UAAW;UAClBsD,QAAQ,EAAGC,CAAC,IAAKtD,aAAa,CAACsD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;UAC/CK,IAAI,EAAE,CAAE;UACRZ,IAAI,EAAC;QAAO;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eACF/D,OAAA,CAACR,MAAM;UACL+E,IAAI,EAAC,SAAS;UACdC,IAAI,eAAExE,OAAA,CAACF,YAAY;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBU,OAAO,EAAEnC,mBAAoB;UAC7B9B,OAAO,EAAEE,UAAW;UACpBgD,IAAI,EAAC,OAAO;UACZF,SAAS,EAAC,+BAA+B;UAAAC,QAAA,EAC1C;QAED;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/D,OAAA;MAAKwD,SAAS,EAAC,WAAW;MAAAC,QAAA,EACvBnD,QAAQ,CAACoE,MAAM,GAAG,CAAC,GAClBpE,QAAQ,CAACqE,GAAG,CAAEC,OAAO,iBACnB5E,OAAA;QAAsBwD,SAAS,EAAC,iCAAiC;QAAAC,QAAA,eAC/DzD,OAAA;UAAKwD,SAAS,EAAC,4BAA4B;UAAAC,QAAA,gBACzCzD,OAAA,CAACN,MAAM;YACL8E,IAAI,eAAExE,OAAA,CAACH,YAAY;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBP,SAAS,EAAEoB,OAAO,CAACxC,OAAO,GAAG,cAAc,GAAG;UAAc;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7D,CAAC,eACF/D,OAAA;YAAKwD,SAAS,EAAC,QAAQ;YAAAC,QAAA,gBACrBzD,OAAA;cAAKwD,SAAS,EAAC,kCAAkC;cAAAC,QAAA,gBAC/CzD,OAAA;gBAAMwD,SAAS,EAAC,6BAA6B;gBAAAC,QAAA,EAC1CmB,OAAO,CAAC3C;cAAM;gBAAA2B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACX,CAAC,EACNa,OAAO,CAACxC,OAAO,iBACdpC,OAAA;gBAAMwD,SAAS,EAAC,wEAAwE;gBAAAC,QAAA,EAAC;cAEzF;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACP,eACD/D,OAAA;gBAAMwD,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACpCT,UAAU,CAAC4B,OAAO,CAACzC,SAAS;cAAC;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC1B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACN/D,OAAA;cAAGwD,SAAS,EAAC,oCAAoC;cAAAC,QAAA,EAC9CmB,OAAO,CAAC1C;YAAO;cAAA0B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,EAGHa,OAAO,CAACvC,OAAO,IAAIuC,OAAO,CAACvC,OAAO,CAACqC,MAAM,GAAG,CAAC,iBAC5C1E,OAAA;cAAKwD,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAC5BmB,OAAO,CAACvC,OAAO,CAACsC,GAAG,CAAEE,KAAK,iBACzB7E,OAAA;gBAAoBwD,SAAS,EAAC,sDAAsD;gBAAAC,QAAA,gBAClFzD,OAAA,CAACN,MAAM;kBACLgE,IAAI,EAAC,OAAO;kBACZc,IAAI,eAAExE,OAAA,CAACH,YAAY;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBP,SAAS,EAAEqB,KAAK,CAACzC,OAAO,GAAG,cAAc,GAAG;gBAAc;kBAAAwB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3D,CAAC,eACF/D,OAAA;kBAAKwD,SAAS,EAAC,QAAQ;kBAAAC,QAAA,gBACrBzD,OAAA;oBAAKwD,SAAS,EAAC,kCAAkC;oBAAAC,QAAA,gBAC/CzD,OAAA;sBAAMwD,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAChDoB,KAAK,CAAC5C;oBAAM;sBAAA2B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACT,CAAC,EACNc,KAAK,CAACzC,OAAO,iBACZpC,OAAA;sBAAMwD,SAAS,EAAC,0EAA0E;sBAAAC,QAAA,EAAC;oBAE3F;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACP,eACD/D,OAAA;sBAAMwD,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EACpCT,UAAU,CAAC6B,KAAK,CAAC1C,SAAS;oBAAC;sBAAAyB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC,eACN/D,OAAA;oBAAGwD,SAAS,EAAC,uCAAuC;oBAAAC,QAAA,EACjDoB,KAAK,CAAC3C;kBAAO;oBAAA0B,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACb,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC;cAAA,GAvBEc,KAAK,CAAC7C,EAAE;gBAAA4B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAwBb,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CACN,EAGA/C,UAAU,KAAK4D,OAAO,CAAC5C,EAAE,gBACxBhC,OAAA;cAAKwD,SAAS,EAAC,uDAAuD;cAAAC,QAAA,eACpEzD,OAAA;gBAAKwD,SAAS,EAAC,WAAW;gBAAAC,QAAA,gBACxBzD,OAAA,CAACP,KAAK;kBACJuE,WAAW,EAAC,WAAW;kBACvBC,KAAK,EAAEnD,QAAS;kBAChBoD,QAAQ,EAAGC,CAAC,IAAKpD,WAAW,CAACoD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBAC7CI,MAAM,eAAErE,OAAA,CAACH,YAAY;oBAAA+D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBL,IAAI,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACF/D,OAAA,CAACC,QAAQ;kBACP+D,WAAW,EAAC,qBAAkB;kBAC9BC,KAAK,EAAE/C,YAAa;kBACpBgD,QAAQ,EAAGC,CAAC,IAAKhD,eAAe,CAACgD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;kBACjDK,IAAI,EAAE,CAAE;kBACRZ,IAAI,EAAC;gBAAO;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACb,CAAC,eACF/D,OAAA;kBAAKwD,SAAS,EAAC,gBAAgB;kBAAAC,QAAA,gBAC7BzD,OAAA,CAACR,MAAM;oBACL+E,IAAI,EAAC,SAAS;oBACdb,IAAI,EAAC,OAAO;oBACZe,OAAO,EAAEA,CAAA,KAAM3B,iBAAiB,CAAC8B,OAAO,CAAC5C,EAAE,CAAE;oBAC7CxB,OAAO,EAAEE,UAAW;oBACpB8C,SAAS,EAAC,+BAA+B;oBAAAC,QAAA,EAC1C;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC,eACT/D,OAAA,CAACR,MAAM;oBACLkE,IAAI,EAAC,OAAO;oBACZe,OAAO,EAAEA,CAAA,KAAM;sBACbxD,aAAa,CAAC,IAAI,CAAC;sBACnBE,eAAe,CAAC,EAAE,CAAC;oBACrB,CAAE;oBAAAsC,QAAA,EACH;kBAED;oBAAAG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAQ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,gBAEN/D,OAAA;cAAKwD,SAAS,EAAC,MAAM;cAAAC,QAAA,eACnBzD,OAAA,CAACR,MAAM;gBACL+E,IAAI,EAAC,MAAM;gBACXb,IAAI,EAAC,OAAO;gBACZe,OAAO,EAAEA,CAAA,KAAMxD,aAAa,CAAC2D,OAAO,CAAC5C,EAAE,CAAE;gBACzCwB,SAAS,EAAC,8CAA8C;gBAAAC,QAAA,EACzD;cAED;gBAAAG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC,GA9GEa,OAAO,CAAC5C,EAAE;QAAA4B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OA+Gf,CACN,CAAC,gBAEF/D,OAAA;QAAKwD,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAChCzD,OAAA;UAAKwD,SAAS,EAAC,oBAAoB;UAAAC,QAAA,eACjCzD,OAAA;YAAKwD,SAAS,EAAC,mBAAmB;YAACsB,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAvB,QAAA,eACtFzD,OAAA;cAAMiF,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,GAAI;cAACC,CAAC,EAAC;YAA+J;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtO;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACN/D,OAAA;UAAIwD,SAAS,EAAC,wCAAwC;UAAAC,QAAA,EAAC;QAEvD;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACL/D,OAAA;UAAGwD,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD;IACN;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC1D,EAAA,CAlVIH,eAA+C;AAAAmF,EAAA,GAA/CnF,eAA+C;AAoVrD,eAAeA,eAAe;AAAC,IAAAmF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}