{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport UnifiedSidebar from './UnifiedSidebar';\nimport CommentsSection from './CommentsSection';\nimport AdPlacement from './AdPlacement';\nimport { Pagination, Spin, Alert } from 'antd';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst StandardLevelPage = ({\n  config\n}) => {\n  _s();\n  const {\n    translations\n  } = useLanguage();\n  const [scholarships, setScholarships] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    page: 1,\n    limit: 6,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Handle scholarship card click\n  const handleScholarshipClick = (id, slug) => {\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      const params = new URLSearchParams({\n        level: config.level,\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);\n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n      const data = await response.json();\n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => {\n          var _data$pagination, _data$pagination2, _data$pagination3, _data$pagination4;\n          return {\n            ...prev,\n            total: ((_data$pagination = data.pagination) === null || _data$pagination === void 0 ? void 0 : _data$pagination.total) || 0,\n            totalPages: ((_data$pagination2 = data.pagination) === null || _data$pagination2 === void 0 ? void 0 : _data$pagination2.totalPages) || 0,\n            hasNextPage: ((_data$pagination3 = data.pagination) === null || _data$pagination3 === void 0 ? void 0 : _data$pagination3.hasNextPage) || false,\n            hasPreviousPage: ((_data$pagination4 = data.pagination) === null || _data$pagination4 === void 0 ? void 0 : _data$pagination4.hasPreviousPage) || false\n          };\n        });\n      } else {\n        throw new Error(data.message || 'Failed to load scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, config.level]);\n  const handlePageChange = page => {\n    setPagination(prev => ({\n      ...prev,\n      page\n    }));\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  const sidebarConfig = {\n    type: 'levels',\n    currentItem: config.level,\n    limit: 10\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(Helmet, {\n      children: [/*#__PURE__*/_jsxDEV(\"title\", {\n        children: config.title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"description\",\n        content: config.description\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"meta\", {\n        name: \"keywords\",\n        content: config.keywords\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 134,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-16 sm:pt-18 md:pt-20 lg:pt-22\",\n      children: [config.heroArticle && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-100 py-6 md:py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"article\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                className: \"text-2xl md:text-3xl font-bold text-gray-900 mb-2\",\n                children: [\"Bourses de \", config.level, \" Disponibles\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-base\",\n                children: [\"Explorez nos opportunit\\xE9s de financement pour vos \\xE9tudes de \", config.level.toLowerCase()]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 153,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"prose max-w-none\",\n              children: config.heroArticle.content.map((paragraph, index) => /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\",\n                children: paragraph.split('\\n').map((line, lineIndex) => /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: [line, lineIndex < paragraph.split('\\n').length - 1 && /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 165,\n                    columnNumber: 76\n                  }, this)]\n                }, lineIndex, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 25\n                }, this))\n              }, index, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 159,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:block py-8 bg-white\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\",\n          children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n            adSlot: \"1234567890\",\n            adSize: \"leaderboard\",\n            responsive: true\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 lg:py-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col lg:flex-row gap-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:w-2/3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"scholarships-section\",\n              className: \"mb-8\",\n              children: loading ? /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-center items-center py-16\",\n                children: /*#__PURE__*/_jsxDEV(Spin, {\n                  size: \"large\",\n                  tip: \"Chargement des bourses...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 199,\n                columnNumber: 19\n              }, this) : error ? /*#__PURE__*/_jsxDEV(Alert, {\n                message: \"Erreur\",\n                description: error,\n                type: \"error\",\n                showIcon: true,\n                className: \"mb-6 rounded-xl shadow-md\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 203,\n                columnNumber: 19\n              }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"mb-8 md:hidden\",\n                  children: /*#__PURE__*/_jsxDEV(AdPlacement, {\n                    adSlot: \"4567890123\",\n                    adSize: \"rectangle\",\n                    responsive: true,\n                    fullWidth: true\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 214,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 213,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2\",\n                  children: scholarships.map((scholarship, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"animate-fade-in\",\n                    style: {\n                      animationDelay: `${index * 0.1}s`\n                    },\n                    children: /*#__PURE__*/_jsxDEV(EnhancedScholarshipCard, {\n                      id: scholarship.id,\n                      title: scholarship.title,\n                      thumbnail: scholarship.thumbnail,\n                      deadline: scholarship.deadline,\n                      isOpen: scholarship.isOpen,\n                      level: scholarship.level,\n                      country: scholarship.country,\n                      fundingSource: scholarship.fundingSource,\n                      onClick: handleScholarshipClick,\n                      index: index\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 225,\n                      columnNumber: 27\n                    }, this)\n                  }, scholarship.id, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 224,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 222,\n                  columnNumber: 21\n                }, this), pagination.total > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex justify-center mt-12\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"bg-white rounded-xl shadow-sm border border-gray-200 p-4\",\n                    children: /*#__PURE__*/_jsxDEV(Pagination, {\n                      current: pagination.page,\n                      total: pagination.total,\n                      pageSize: pagination.limit,\n                      onChange: handlePageChange,\n                      showSizeChanger: false,\n                      showQuickJumper: false,\n                      showTotal: false,\n                      size: \"default\",\n                      className: \"professional-pagination\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 245,\n                      columnNumber: 27\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 244,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 23\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 195,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(UnifiedSidebar, {\n            config: sidebarConfig,\n            className: \"lg:w-1/3\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 189,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gray-50 py-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(CommentsSection, {\n            pageType: \"level\",\n            pageId: config.level\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white py-4 md:py-6 lg:py-8 border-t border-gray-100\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-4xl mx-auto\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              id: \"info-section\",\n              children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                className: \"text-base md:text-lg lg:text-xl font-semibold text-gray-900 mb-3 md:mb-4\",\n                children: config.infoTitle\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 287,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-700 leading-relaxed mb-4 md:mb-6 text-sm md:text-base text-justify\",\n                children: config.infoContent\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 291,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3\",\n                children: config.benefits.map((benefit, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-2 md:space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                    className: \"w-3 h-3 md:w-4 md:h-4 text-green-500 mt-0.5 flex-shrink-0\",\n                    fill: \"currentColor\",\n                    viewBox: \"0 0 20 20\",\n                    children: /*#__PURE__*/_jsxDEV(\"path\", {\n                      fillRule: \"evenodd\",\n                      d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                      clipRule: \"evenodd\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 299,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 298,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                    className: \"text-gray-700 text-xs md:text-sm leading-relaxed\",\n                    children: benefit\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 301,\n                    columnNumber: 23\n                  }, this)]\n                }, index, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 286,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 285,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 284,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 283,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(StandardLevelPage, \"upsU7Mx3/9boKwHi+QgYC7+wbkA=\", false, function () {\n  return [useLanguage];\n});\n_c = StandardLevelPage;\nexport default StandardLevelPage;\nvar _c;\n$RefreshReg$(_c, \"StandardLevelPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "<PERSON><PERSON><PERSON>", "useLanguage", "EnhancedScholarshipCard", "UnifiedSidebar", "CommentsSection", "AdPlacement", "Pagination", "Spin", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "StandardLevelPage", "config", "_s", "translations", "scholarships", "setScholarships", "loading", "setLoading", "error", "setError", "pagination", "setPagination", "total", "page", "limit", "totalPages", "hasNextPage", "hasPreviousPage", "handleScholarshipClick", "id", "slug", "window", "location", "href", "fetchScholarships", "params", "URLSearchParams", "level", "toString", "response", "fetch", "process", "env", "REACT_APP_API_URL", "apiEndpoint", "ok", "Error", "data", "json", "success", "prev", "_data$pagination", "_data$pagination2", "_data$pagination3", "_data$pagination4", "message", "console", "handlePageChange", "scrollTo", "top", "behavior", "sidebarConfig", "type", "currentItem", "children", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "content", "description", "keywords", "className", "heroArticle", "toLowerCase", "map", "paragraph", "index", "split", "line", "lineIndex", "length", "adSlot", "adSize", "responsive", "size", "tip", "showIcon", "fullWidth", "scholarship", "style", "animationDelay", "thumbnail", "deadline", "isOpen", "country", "fundingSource", "onClick", "current", "pageSize", "onChange", "showSizeChanger", "showQuickJumper", "showTotal", "pageType", "pageId", "infoTitle", "infoContent", "benefits", "benefit", "fill", "viewBox", "fillRule", "d", "clipRule", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Desktop/MaBourseWebsitecopy2backup/src/components/StandardLevelPage.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Helmet } from 'react-helmet-async';\nimport { useLanguage } from '../context/LanguageContext';\nimport EnhancedScholarshipCard from './EnhancedScholarshipCard';\nimport UnifiedSidebar from './UnifiedSidebar';\nimport CommentsSection from './CommentsSection';\nimport AdPlacement from './AdPlacement';\nimport { Pagination, Spin, Alert } from 'antd';\n\ninterface Scholarship {\n  id: number;\n  title: string;\n  description: string;\n  level: string;\n  country: string;\n  deadline: string;\n  isOpen: boolean;\n  thumbnail: string;\n  fundingSource?: string;\n}\n\ninterface PaginationData {\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n  hasNextPage: boolean;\n  hasPreviousPage: boolean;\n}\n\ninterface HeroArticle {\n  category: string;\n  content: string[];\n}\n\ninterface LevelPageConfig {\n  level: string;\n  title: string;\n  description: string;\n  keywords: string;\n  heroTitle: string;\n  heroSubtitle: string;\n  heroArticle?: HeroArticle;\n  infoTitle: string;\n  infoContent: string;\n  benefits: string[];\n  apiEndpoint: string;\n}\n\ninterface StandardLevelPageProps {\n  config: LevelPageConfig;\n}\n\nconst StandardLevelPage: React.FC<StandardLevelPageProps> = ({ config }) => {\n  const { translations } = useLanguage();\n  const [scholarships, setScholarships] = useState<Scholarship[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [pagination, setPagination] = useState<PaginationData>({\n    total: 0,\n    page: 1,\n    limit: 6,\n    totalPages: 0,\n    hasNextPage: false,\n    hasPreviousPage: false\n  });\n\n  // Handle scholarship card click\n  const handleScholarshipClick = (id: number, slug?: string) => {\n    if (slug) {\n      window.location.href = `/bourse/${slug}`;\n    } else {\n      window.location.href = `/scholarships/${id}`;\n    }\n  };\n\n  const fetchScholarships = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      const params = new URLSearchParams({\n        level: config.level,\n        page: pagination.page.toString(),\n        limit: pagination.limit.toString()\n      });\n\n      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}${config.apiEndpoint}?${params}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch scholarships');\n      }\n\n      const data = await response.json();\n      \n      if (data.success) {\n        setScholarships(data.data || []);\n        setPagination(prev => ({\n          ...prev,\n          total: data.pagination?.total || 0,\n          totalPages: data.pagination?.totalPages || 0,\n          hasNextPage: data.pagination?.hasNextPage || false,\n          hasPreviousPage: data.pagination?.hasPreviousPage || false\n        }));\n      } else {\n        throw new Error(data.message || 'Failed to load scholarships');\n      }\n    } catch (error) {\n      console.error('Error fetching scholarships:', error);\n      setError('Impossible de charger les bourses. Veuillez réessayer plus tard.');\n      setScholarships([]);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchScholarships();\n  }, [pagination.page, config.level]);\n\n  const handlePageChange = (page: number) => {\n    setPagination(prev => ({ ...prev, page }));\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  const sidebarConfig = {\n    type: 'levels' as const,\n    currentItem: config.level,\n    limit: 10\n  };\n\n  return (\n    <>\n      <Helmet>\n        <title>{config.title}</title>\n        <meta name=\"description\" content={config.description} />\n        <meta name=\"keywords\" content={config.keywords} />\n      </Helmet>\n\n      <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 pt-16 sm:pt-18 md:pt-20 lg:pt-22\">\n\n\n        {/* Hero Article with Integrated Title - Clean Professional */}\n        {config.heroArticle && (\n          <div className=\"bg-gray-100 py-6 md:py-8\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <article>\n                {/* Main Title - Integrated */}\n                <div className=\"mb-6\">\n                  <h1 className=\"text-2xl md:text-3xl font-bold text-gray-900 mb-2\">\n                    Bourses de {config.level} Disponibles\n                  </h1>\n                  <p className=\"text-gray-600 text-base\">\n                    Explorez nos opportunités de financement pour vos études de {config.level.toLowerCase()}\n                  </p>\n                </div>\n\n                {/* Article Content - Full Width */}\n                <div className=\"prose max-w-none\">\n                  {config.heroArticle.content.map((paragraph, index) => (\n                    <p key={index} className=\"text-gray-700 leading-relaxed mb-4 text-sm md:text-base text-justify\">\n                      {paragraph.split('\\n').map((line, lineIndex) => (\n                        <span key={lineIndex}>\n                          {line}\n                          {lineIndex < paragraph.split('\\n').length - 1 && <br />}\n                        </span>\n                      ))}\n                    </p>\n                  ))}\n                </div>\n              </article>\n            </div>\n          </div>\n        )}\n\n        {/* Desktop Ad - Only visible on large screens */}\n        <div className=\"hidden lg:block py-8 bg-white\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center\">\n            <AdPlacement\n              adSlot=\"1234567890\"\n              adSize=\"leaderboard\"\n              responsive={true}\n            />\n          </div>\n        </div>\n\n        {/* Content Section */}\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 md:py-12 lg:py-16\">\n          <div className=\"flex flex-col lg:flex-row gap-8\">\n            {/* Main Content */}\n            <div className=\"lg:w-2/3\">\n\n\n              {/* Scholarships Grid - Clean & Focused */}\n              <div id=\"scholarships-section\" className=\"mb-8\">\n\n\n                {loading ? (\n                  <div className=\"flex justify-center items-center py-16\">\n                    <Spin size=\"large\" tip=\"Chargement des bourses...\" />\n                  </div>\n                ) : error ? (\n                  <Alert\n                    message=\"Erreur\"\n                    description={error}\n                    type=\"error\"\n                    showIcon\n                    className=\"mb-6 rounded-xl shadow-md\"\n                  />\n                ) : (\n                  <>\n                    {/* Mobile Ad - Only visible on small screens */}\n                    <div className=\"mb-8 md:hidden\">\n                      <AdPlacement\n                        adSlot=\"4567890123\"\n                        adSize=\"rectangle\"\n                        responsive={true}\n                        fullWidth={true}\n                      />\n                    </div>\n\n                    <div className=\"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-2\">\n                      {scholarships.map((scholarship, index) => (\n                        <div key={scholarship.id} className=\"animate-fade-in\" style={{ animationDelay: `${index * 0.1}s` }}>\n                          <EnhancedScholarshipCard\n                            id={scholarship.id}\n                            title={scholarship.title}\n                            thumbnail={scholarship.thumbnail}\n                            deadline={scholarship.deadline}\n                            isOpen={scholarship.isOpen}\n                            level={scholarship.level}\n                            country={scholarship.country}\n                            fundingSource={scholarship.fundingSource}\n                            onClick={handleScholarshipClick}\n                            index={index}\n                          />\n                        </div>\n                      ))}\n                    </div>\n\n                    {/* Professional Pagination */}\n                    {pagination.total > 0 && (\n                      <div className=\"flex justify-center mt-12\">\n                        <div className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-4\">\n                          <Pagination\n                            current={pagination.page}\n                            total={pagination.total}\n                            pageSize={pagination.limit}\n                            onChange={handlePageChange}\n                            showSizeChanger={false}\n                            showQuickJumper={false}\n                            showTotal={false}\n                            size=\"default\"\n                            className=\"professional-pagination\"\n                          />\n                        </div>\n                      </div>\n                    )}\n                  </>\n                )}\n              </div>\n            </div>\n\n            {/* Sidebar */}\n            <UnifiedSidebar\n              config={sidebarConfig}\n              className=\"lg:w-1/3\"\n            />\n          </div>\n        </div>\n\n        {/* Comments Section */}\n        <div className=\"bg-gray-50 py-8\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <CommentsSection\n              pageType=\"level\"\n              pageId={config.level}\n            />\n          </div>\n        </div>\n\n        {/* Info Section - Fully Responsive */}\n        <div className=\"bg-white py-4 md:py-6 lg:py-8 border-t border-gray-100\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <div className=\"max-w-4xl mx-auto\">\n              <div id=\"info-section\">\n                <h2 className=\"text-base md:text-lg lg:text-xl font-semibold text-gray-900 mb-3 md:mb-4\">\n                  {config.infoTitle}\n                </h2>\n\n                <p className=\"text-gray-700 leading-relaxed mb-4 md:mb-6 text-sm md:text-base text-justify\">\n                  {config.infoContent}\n                </p>\n\n                <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-2 md:gap-3\">\n                  {config.benefits.map((benefit, index) => (\n                    <div key={index} className=\"flex items-start space-x-2 md:space-x-3\">\n                      <svg className=\"w-3 h-3 md:w-4 md:h-4 text-green-500 mt-0.5 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                      <span className=\"text-gray-700 text-xs md:text-sm leading-relaxed\">\n                        {benefit}\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n\n      </div>\n    </>\n  );\n};\n\nexport default StandardLevelPage;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,oBAAoB;AAC3C,SAASC,WAAW,QAAQ,4BAA4B;AACxD,OAAOC,uBAAuB,MAAM,2BAA2B;AAC/D,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AA8C/C,MAAMC,iBAAmD,GAAGA,CAAC;EAAEC;AAAO,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM;IAAEC;EAAa,CAAC,GAAGf,WAAW,CAAC,CAAC;EACtC,MAAM,CAACgB,YAAY,EAAEC,eAAe,CAAC,GAAGpB,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACqB,OAAO,EAAEC,UAAU,CAAC,GAAGtB,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACuB,KAAK,EAAEC,QAAQ,CAAC,GAAGxB,QAAQ,CAAgB,IAAI,CAAC;EACvD,MAAM,CAACyB,UAAU,EAAEC,aAAa,CAAC,GAAG1B,QAAQ,CAAiB;IAC3D2B,KAAK,EAAE,CAAC;IACRC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE,CAAC;IACbC,WAAW,EAAE,KAAK;IAClBC,eAAe,EAAE;EACnB,CAAC,CAAC;;EAEF;EACA,MAAMC,sBAAsB,GAAGA,CAACC,EAAU,EAAEC,IAAa,KAAK;IAC5D,IAAIA,IAAI,EAAE;MACRC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,WAAWH,IAAI,EAAE;IAC1C,CAAC,MAAM;MACLC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,iBAAiBJ,EAAE,EAAE;IAC9C;EACF,CAAC;EAED,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFjB,UAAU,CAAC,IAAI,CAAC;MAChBE,QAAQ,CAAC,IAAI,CAAC;MAEd,MAAMgB,MAAM,GAAG,IAAIC,eAAe,CAAC;QACjCC,KAAK,EAAE1B,MAAM,CAAC0B,KAAK;QACnBd,IAAI,EAAEH,UAAU,CAACG,IAAI,CAACe,QAAQ,CAAC,CAAC;QAChCd,KAAK,EAAEJ,UAAU,CAACI,KAAK,CAACc,QAAQ,CAAC;MACnC,CAAC,CAAC;MAEF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,uBAAuB,GAAGhC,MAAM,CAACiC,WAAW,IAAIT,MAAM,EAAE,CAAC;MAE1H,IAAI,CAACI,QAAQ,CAACM,EAAE,EAAE;QAChB,MAAM,IAAIC,KAAK,CAAC,8BAA8B,CAAC;MACjD;MAEA,MAAMC,IAAI,GAAG,MAAMR,QAAQ,CAACS,IAAI,CAAC,CAAC;MAElC,IAAID,IAAI,CAACE,OAAO,EAAE;QAChBlC,eAAe,CAACgC,IAAI,CAACA,IAAI,IAAI,EAAE,CAAC;QAChC1B,aAAa,CAAC6B,IAAI;UAAA,IAAAC,gBAAA,EAAAC,iBAAA,EAAAC,iBAAA,EAAAC,iBAAA;UAAA,OAAK;YACrB,GAAGJ,IAAI;YACP5B,KAAK,EAAE,EAAA6B,gBAAA,GAAAJ,IAAI,CAAC3B,UAAU,cAAA+B,gBAAA,uBAAfA,gBAAA,CAAiB7B,KAAK,KAAI,CAAC;YAClCG,UAAU,EAAE,EAAA2B,iBAAA,GAAAL,IAAI,CAAC3B,UAAU,cAAAgC,iBAAA,uBAAfA,iBAAA,CAAiB3B,UAAU,KAAI,CAAC;YAC5CC,WAAW,EAAE,EAAA2B,iBAAA,GAAAN,IAAI,CAAC3B,UAAU,cAAAiC,iBAAA,uBAAfA,iBAAA,CAAiB3B,WAAW,KAAI,KAAK;YAClDC,eAAe,EAAE,EAAA2B,iBAAA,GAAAP,IAAI,CAAC3B,UAAU,cAAAkC,iBAAA,uBAAfA,iBAAA,CAAiB3B,eAAe,KAAI;UACvD,CAAC;QAAA,CAAC,CAAC;MACL,CAAC,MAAM;QACL,MAAM,IAAImB,KAAK,CAACC,IAAI,CAACQ,OAAO,IAAI,6BAA6B,CAAC;MAChE;IACF,CAAC,CAAC,OAAOrC,KAAK,EAAE;MACdsC,OAAO,CAACtC,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDC,QAAQ,CAAC,kEAAkE,CAAC;MAC5EJ,eAAe,CAAC,EAAE,CAAC;IACrB,CAAC,SAAS;MACRE,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAEDrB,SAAS,CAAC,MAAM;IACdsC,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,CAACd,UAAU,CAACG,IAAI,EAAEZ,MAAM,CAAC0B,KAAK,CAAC,CAAC;EAEnC,MAAMoB,gBAAgB,GAAIlC,IAAY,IAAK;IACzCF,aAAa,CAAC6B,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE3B;IAAK,CAAC,CAAC,CAAC;IAC1CQ,MAAM,CAAC2B,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBC,IAAI,EAAE,QAAiB;IACvBC,WAAW,EAAEpD,MAAM,CAAC0B,KAAK;IACzBb,KAAK,EAAE;EACT,CAAC;EAED,oBACEjB,OAAA,CAAAE,SAAA;IAAAuD,QAAA,gBACEzD,OAAA,CAACV,MAAM;MAAAmE,QAAA,gBACLzD,OAAA;QAAAyD,QAAA,EAAQrD,MAAM,CAACsD;MAAK;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAC7B9D,OAAA;QAAM+D,IAAI,EAAC,aAAa;QAACC,OAAO,EAAE5D,MAAM,CAAC6D;MAAY;QAAAN,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxD9D,OAAA;QAAM+D,IAAI,EAAC,UAAU;QAACC,OAAO,EAAE5D,MAAM,CAAC8D;MAAS;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC5C,CAAC,eAET9D,OAAA;MAAKmE,SAAS,EAAC,yFAAyF;MAAAV,QAAA,GAIrGrD,MAAM,CAACgE,WAAW,iBACjBpE,OAAA;QAAKmE,SAAS,EAAC,0BAA0B;QAAAV,QAAA,eACvCzD,OAAA;UAAKmE,SAAS,EAAC,wCAAwC;UAAAV,QAAA,eACrDzD,OAAA;YAAAyD,QAAA,gBAEEzD,OAAA;cAAKmE,SAAS,EAAC,MAAM;cAAAV,QAAA,gBACnBzD,OAAA;gBAAImE,SAAS,EAAC,mDAAmD;gBAAAV,QAAA,GAAC,aACrD,EAACrD,MAAM,CAAC0B,KAAK,EAAC,cAC3B;cAAA;gBAAA6B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL9D,OAAA;gBAAGmE,SAAS,EAAC,yBAAyB;gBAAAV,QAAA,GAAC,oEACuB,EAACrD,MAAM,CAAC0B,KAAK,CAACuC,WAAW,CAAC,CAAC;cAAA;gBAAAV,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eAGN9D,OAAA;cAAKmE,SAAS,EAAC,kBAAkB;cAAAV,QAAA,EAC9BrD,MAAM,CAACgE,WAAW,CAACJ,OAAO,CAACM,GAAG,CAAC,CAACC,SAAS,EAAEC,KAAK,kBAC/CxE,OAAA;gBAAemE,SAAS,EAAC,sEAAsE;gBAAAV,QAAA,EAC5Fc,SAAS,CAACE,KAAK,CAAC,IAAI,CAAC,CAACH,GAAG,CAAC,CAACI,IAAI,EAAEC,SAAS,kBACzC3E,OAAA;kBAAAyD,QAAA,GACGiB,IAAI,EACJC,SAAS,GAAGJ,SAAS,CAACE,KAAK,CAAC,IAAI,CAAC,CAACG,MAAM,GAAG,CAAC,iBAAI5E,OAAA;oBAAA2D,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CAAC;gBAAA,GAF9Ca,SAAS;kBAAAhB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGd,CACP;cAAC,GANIU,KAAK;gBAAAb,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAOV,CACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN,eAGD9D,OAAA;QAAKmE,SAAS,EAAC,+BAA+B;QAAAV,QAAA,eAC5CzD,OAAA;UAAKmE,SAAS,EAAC,oDAAoD;UAAAV,QAAA,eACjEzD,OAAA,CAACL,WAAW;YACVkF,MAAM,EAAC,YAAY;YACnBC,MAAM,EAAC,aAAa;YACpBC,UAAU,EAAE;UAAK;YAAApB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKmE,SAAS,EAAC,+DAA+D;QAAAV,QAAA,eAC5EzD,OAAA;UAAKmE,SAAS,EAAC,iCAAiC;UAAAV,QAAA,gBAE9CzD,OAAA;YAAKmE,SAAS,EAAC,UAAU;YAAAV,QAAA,eAIvBzD,OAAA;cAAKsB,EAAE,EAAC,sBAAsB;cAAC6C,SAAS,EAAC,MAAM;cAAAV,QAAA,EAG5ChD,OAAO,gBACNT,OAAA;gBAAKmE,SAAS,EAAC,wCAAwC;gBAAAV,QAAA,eACrDzD,OAAA,CAACH,IAAI;kBAACmF,IAAI,EAAC,OAAO;kBAACC,GAAG,EAAC;gBAA2B;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClD,CAAC,GACJnD,KAAK,gBACPX,OAAA,CAACF,KAAK;gBACJkD,OAAO,EAAC,QAAQ;gBAChBiB,WAAW,EAAEtD,KAAM;gBACnB4C,IAAI,EAAC,OAAO;gBACZ2B,QAAQ;gBACRf,SAAS,EAAC;cAA2B;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtC,CAAC,gBAEF9D,OAAA,CAAAE,SAAA;gBAAAuD,QAAA,gBAEEzD,OAAA;kBAAKmE,SAAS,EAAC,gBAAgB;kBAAAV,QAAA,eAC7BzD,OAAA,CAACL,WAAW;oBACVkF,MAAM,EAAC,YAAY;oBACnBC,MAAM,EAAC,WAAW;oBAClBC,UAAU,EAAE,IAAK;oBACjBI,SAAS,EAAE;kBAAK;oBAAAxB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN9D,OAAA;kBAAKmE,SAAS,EAAC,sDAAsD;kBAAAV,QAAA,EAClElD,YAAY,CAAC+D,GAAG,CAAC,CAACc,WAAW,EAAEZ,KAAK,kBACnCxE,OAAA;oBAA0BmE,SAAS,EAAC,iBAAiB;oBAACkB,KAAK,EAAE;sBAAEC,cAAc,EAAE,GAAGd,KAAK,GAAG,GAAG;oBAAI,CAAE;oBAAAf,QAAA,eACjGzD,OAAA,CAACR,uBAAuB;sBACtB8B,EAAE,EAAE8D,WAAW,CAAC9D,EAAG;sBACnBoC,KAAK,EAAE0B,WAAW,CAAC1B,KAAM;sBACzB6B,SAAS,EAAEH,WAAW,CAACG,SAAU;sBACjCC,QAAQ,EAAEJ,WAAW,CAACI,QAAS;sBAC/BC,MAAM,EAAEL,WAAW,CAACK,MAAO;sBAC3B3D,KAAK,EAAEsD,WAAW,CAACtD,KAAM;sBACzB4D,OAAO,EAAEN,WAAW,CAACM,OAAQ;sBAC7BC,aAAa,EAAEP,WAAW,CAACO,aAAc;sBACzCC,OAAO,EAAEvE,sBAAuB;sBAChCmD,KAAK,EAAEA;oBAAM;sBAAAb,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACd;kBAAC,GAZMsB,WAAW,CAAC9D,EAAE;oBAAAqC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAanB,CACN;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,EAGLjD,UAAU,CAACE,KAAK,GAAG,CAAC,iBACnBf,OAAA;kBAAKmE,SAAS,EAAC,2BAA2B;kBAAAV,QAAA,eACxCzD,OAAA;oBAAKmE,SAAS,EAAC,0DAA0D;oBAAAV,QAAA,eACvEzD,OAAA,CAACJ,UAAU;sBACTiG,OAAO,EAAEhF,UAAU,CAACG,IAAK;sBACzBD,KAAK,EAAEF,UAAU,CAACE,KAAM;sBACxB+E,QAAQ,EAAEjF,UAAU,CAACI,KAAM;sBAC3B8E,QAAQ,EAAE7C,gBAAiB;sBAC3B8C,eAAe,EAAE,KAAM;sBACvBC,eAAe,EAAE,KAAM;sBACvBC,SAAS,EAAE,KAAM;sBACjBlB,IAAI,EAAC,SAAS;sBACdb,SAAS,EAAC;oBAAyB;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACpC;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACN;cAAA,eACD;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGN9D,OAAA,CAACP,cAAc;YACbW,MAAM,EAAEkD,aAAc;YACtBa,SAAS,EAAC;UAAU;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKmE,SAAS,EAAC,iBAAiB;QAAAV,QAAA,eAC9BzD,OAAA;UAAKmE,SAAS,EAAC,wCAAwC;UAAAV,QAAA,eACrDzD,OAAA,CAACN,eAAe;YACdyG,QAAQ,EAAC,OAAO;YAChBC,MAAM,EAAEhG,MAAM,CAAC0B;UAAM;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN9D,OAAA;QAAKmE,SAAS,EAAC,wDAAwD;QAAAV,QAAA,eACrEzD,OAAA;UAAKmE,SAAS,EAAC,wCAAwC;UAAAV,QAAA,eACrDzD,OAAA;YAAKmE,SAAS,EAAC,mBAAmB;YAAAV,QAAA,eAChCzD,OAAA;cAAKsB,EAAE,EAAC,cAAc;cAAAmC,QAAA,gBACpBzD,OAAA;gBAAImE,SAAS,EAAC,0EAA0E;gBAAAV,QAAA,EACrFrD,MAAM,CAACiG;cAAS;gBAAA1C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf,CAAC,eAEL9D,OAAA;gBAAGmE,SAAS,EAAC,8EAA8E;gBAAAV,QAAA,EACxFrD,MAAM,CAACkG;cAAW;gBAAA3C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB,CAAC,eAEJ9D,OAAA;gBAAKmE,SAAS,EAAC,gDAAgD;gBAAAV,QAAA,EAC5DrD,MAAM,CAACmG,QAAQ,CAACjC,GAAG,CAAC,CAACkC,OAAO,EAAEhC,KAAK,kBAClCxE,OAAA;kBAAiBmE,SAAS,EAAC,yCAAyC;kBAAAV,QAAA,gBAClEzD,OAAA;oBAAKmE,SAAS,EAAC,2DAA2D;oBAACsC,IAAI,EAAC,cAAc;oBAACC,OAAO,EAAC,WAAW;oBAAAjD,QAAA,eAChHzD,OAAA;sBAAM2G,QAAQ,EAAC,SAAS;sBAACC,CAAC,EAAC,oHAAoH;sBAACC,QAAQ,EAAC;oBAAS;sBAAAlD,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClK,CAAC,eACN9D,OAAA;oBAAMmE,SAAS,EAAC,kDAAkD;oBAAAV,QAAA,EAC/D+C;kBAAO;oBAAA7C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA,GANCU,KAAK;kBAAAb,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAOV,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAGH,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACzD,EAAA,CAtQIF,iBAAmD;EAAA,QAC9BZ,WAAW;AAAA;AAAAuH,EAAA,GADhC3G,iBAAmD;AAwQzD,eAAeA,iBAAiB;AAAC,IAAA2G,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}